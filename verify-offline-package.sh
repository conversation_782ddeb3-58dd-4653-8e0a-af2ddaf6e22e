#!/bin/bash

# Black Duck 离线部署包完整性验证脚本
# Author: Deployment Automation
# Version: 1.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Print banner
print_banner() {
    echo ""
    echo "========================================"
    echo "  Black Duck 离线部署包完整性验证"
    echo "========================================"
    echo ""
}

# Check required files
check_required_files() {
    log "检查必需文件..."
    
    local required_files=(
        "docker-compose-offline.yml"
        "load-images.sh"
        ".env"
        "deploy.sh"
        "health-check.sh"
        "setup.sh"
        "configs/nginx-offline.conf"
        "demo/offline.html"
        "docker-images/postgres-15.tar"
        "docker-images/redis-7-alpine.tar"
        "docker-images/nginx-alpine.tar"
        "docker-images/rabbitmq-3-management-alpine.tar"
        "docker-images/checksums.sha256"
        "docker-images/images-info.txt"
        "scripts/backup.sh"
        "scripts/restore.sh"
        "README.md"
        "OFFLINE_DEPLOYMENT.md"
    )
    
    local missing_files=()
    local found_files=0
    
    for file in "${required_files[@]}"; do
        if [ -f "$SCRIPT_DIR/$file" ]; then
            success "✓ $file"
            ((found_files++))
        else
            error "✗ $file (缺失)"
            missing_files+=("$file")
        fi
    done
    
    echo ""
    log "文件检查结果: $found_files/${#required_files[@]} 个文件"
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        success "所有必需文件都存在"
        return 0
    else
        error "缺失 ${#missing_files[@]} 个文件: ${missing_files[*]}"
        return 1
    fi
}

# Check file permissions
check_permissions() {
    log "检查脚本执行权限..."
    
    local scripts=(
        "load-images.sh"
        "deploy.sh"
        "health-check.sh"
        "setup.sh"
        "scripts/backup.sh"
        "scripts/restore.sh"
        "verify-offline-package.sh"
    )
    
    local permission_issues=0
    
    for script in "${scripts[@]}"; do
        if [ -f "$SCRIPT_DIR/$script" ]; then
            if [ -x "$SCRIPT_DIR/$script" ]; then
                success "✓ $script (可执行)"
            else
                warning "⚠ $script (不可执行)"
                ((permission_issues++))
            fi
        fi
    done
    
    if [ $permission_issues -eq 0 ]; then
        success "所有脚本权限正确"
        return 0
    else
        warning "发现 $permission_issues 个权限问题"
        log "运行 'chmod +x *.sh scripts/*.sh' 修复权限问题"
        return 1
    fi
}

# Check Docker images
check_docker_images() {
    log "检查 Docker 镜像文件..."
    
    local images_dir="$SCRIPT_DIR/docker-images"
    local image_files=(
        "postgres-15.tar"
        "redis-7-alpine.tar"
        "nginx-alpine.tar"
        "rabbitmq-3-management-alpine.tar"
    )
    
    local total_size=0
    local valid_images=0
    
    for image_file in "${image_files[@]}"; do
        local image_path="$images_dir/$image_file"
        
        if [ -f "$image_path" ]; then
            local size=$(du -h "$image_path" | cut -f1)
            # Use stat for file size on macOS/BSD
            if command -v stat &> /dev/null; then
                if stat -f%z "$image_path" &> /dev/null; then
                    # BSD stat (macOS)
                    local size_bytes=$(stat -f%z "$image_path")
                else
                    # GNU stat (Linux)
                    local size_bytes=$(stat -c%s "$image_path")
                fi
            else
                # Fallback: estimate from human readable size
                local size_mb=$(echo "$size" | sed 's/M$//' | sed 's/G$/000/')
                local size_bytes=$((size_mb * 1024 * 1024))
            fi

            total_size=$((total_size + size_bytes))

            # Basic file validation (> 10MB)
            if [ "$size_bytes" -gt 10000000 ]; then
                success "✓ $image_file ($size)"
                ((valid_images++))
            else
                error "✗ $image_file (文件太小: $size)"
            fi
        else
            error "✗ $image_file (文件不存在)"
        fi
    done
    
    local total_size_mb=$((total_size / 1024 / 1024))
    echo ""
    log "镜像文件统计: $valid_images/${#image_files[@]} 个有效文件"
    log "总大小: ${total_size_mb}MB"
    
    if [ $valid_images -eq ${#image_files[@]} ]; then
        success "所有镜像文件都存在且大小合理"
        return 0
    else
        error "镜像文件检查失败"
        return 1
    fi
}

# Verify checksums
verify_checksums() {
    log "验证镜像文件校验和..."
    
    local checksums_file="$SCRIPT_DIR/docker-images/checksums.sha256"
    
    if [ ! -f "$checksums_file" ]; then
        warning "校验文件不存在，跳过校验和验证"
        return 0
    fi
    
    cd "$SCRIPT_DIR/docker-images"
    
    # Use appropriate checksum command
    if command -v sha256sum &> /dev/null; then
        if sha256sum -c checksums.sha256 --quiet 2>/dev/null; then
            success "镜像文件校验和验证通过"
            cd "$SCRIPT_DIR"
            return 0
        else
            error "镜像文件校验和验证失败"
            cd "$SCRIPT_DIR"
            return 1
        fi
    elif command -v shasum &> /dev/null; then
        if shasum -a 256 -c checksums.sha256 --quiet 2>/dev/null; then
            success "镜像文件校验和验证通过"
            cd "$SCRIPT_DIR"
            return 0
        else
            error "镜像文件校验和验证失败"
            cd "$SCRIPT_DIR"
            return 1
        fi
    else
        warning "无法找到校验工具，跳过校验和验证"
        cd "$SCRIPT_DIR"
        return 0
    fi
}

# Check configuration files
check_configurations() {
    log "检查配置文件语法..."
    
    local config_issues=0
    
    # Check docker-compose file
    if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
        if docker-compose -f docker-compose-offline.yml config --quiet 2>/dev/null; then
            success "✓ docker-compose-offline.yml 语法正确"
        else
            error "✗ docker-compose-offline.yml 语法错误"
            ((config_issues++))
        fi
    elif command -v docker &> /dev/null; then
        if docker compose -f docker-compose-offline.yml config --quiet 2>/dev/null; then
            success "✓ docker-compose-offline.yml 语法正确"
        else
            error "✗ docker-compose-offline.yml 语法错误"
            ((config_issues++))
        fi
    else
        warning "Docker 未安装，跳过 compose 文件语法检查"
    fi
    
    # Check nginx config
    if [ -f "configs/nginx-offline.conf" ]; then
        # Basic syntax check
        if grep -q "server {" "configs/nginx-offline.conf" && grep -q "location" "configs/nginx-offline.conf"; then
            success "✓ nginx-offline.conf 基本语法正确"
        else
            warning "⚠ nginx-offline.conf 可能存在语法问题"
            ((config_issues++))
        fi
    fi
    
    if [ $config_issues -eq 0 ]; then
        success "配置文件检查通过"
        return 0
    else
        warning "发现 $config_issues 个配置问题"
        return 1
    fi
}

# Generate package report
generate_report() {
    log "生成部署包报告..."
    
    local report_file="$SCRIPT_DIR/package-verification-report.txt"
    
    {
        echo "Black Duck 离线部署包验证报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "部署包信息:"
        echo "- 位置: $SCRIPT_DIR"
        echo "- 总大小: $(du -sh "$SCRIPT_DIR" | cut -f1)"
        echo ""
        
        echo "镜像文件:"
        if [ -d "$SCRIPT_DIR/docker-images" ]; then
            ls -lh "$SCRIPT_DIR/docker-images"/*.tar 2>/dev/null || echo "无镜像文件"
        fi
        echo ""
        
        echo "配置文件:"
        echo "- docker-compose-offline.yml: $([ -f docker-compose-offline.yml ] && echo "存在" || echo "缺失")"
        echo "- .env: $([ -f .env ] && echo "存在" || echo "缺失")"
        echo "- nginx-offline.conf: $([ -f configs/nginx-offline.conf ] && echo "存在" || echo "缺失")"
        echo ""
        
        echo "脚本文件:"
        echo "- load-images.sh: $([ -x load-images.sh ] && echo "可执行" || echo "不可执行")"
        echo "- deploy.sh: $([ -x deploy.sh ] && echo "可执行" || echo "不可执行")"
        echo "- health-check.sh: $([ -x health-check.sh ] && echo "可执行" || echo "不可执行")"
        echo ""
        
        echo "文档文件:"
        echo "- README.md: $([ -f README.md ] && echo "存在" || echo "缺失")"
        echo "- OFFLINE_DEPLOYMENT.md: $([ -f OFFLINE_DEPLOYMENT.md ] && echo "存在" || echo "缺失")"
        echo ""
        
    } > "$report_file"
    
    success "验证报告已保存到: $report_file"
}

# Main verification function
main() {
    print_banner
    
    local checks_passed=0
    local total_checks=5
    
    if check_required_files; then
        ((checks_passed++))
    fi
    
    if check_permissions; then
        ((checks_passed++))
    fi
    
    if check_docker_images; then
        ((checks_passed++))
    fi
    
    if verify_checksums; then
        ((checks_passed++))
    fi
    
    if check_configurations; then
        ((checks_passed++))
    fi
    
    generate_report
    
    echo ""
    echo "========================================"
    echo "验证结果总结"
    echo "========================================"
    
    if [ $checks_passed -eq $total_checks ]; then
        success "所有检查通过 ($checks_passed/$total_checks)"
        echo ""
        echo "🎉 离线部署包完整且可用！"
        echo ""
        echo "下一步操作:"
        echo "1. 将部署包传输到目标服务器"
        echo "2. 运行 './load-images.sh' 加载镜像"
        echo "3. 运行 './deploy.sh offline' 开始部署"
        return 0
    else
        error "部分检查失败 ($checks_passed/$total_checks)"
        echo ""
        echo "❌ 离线部署包存在问题，请修复后重新验证"
        return 1
    fi
}

# Handle script arguments
case "${1:-}" in
    ""|"verify")
        main
        ;;
    "files")
        check_required_files
        ;;
    "permissions")
        check_permissions
        ;;
    "images")
        check_docker_images
        ;;
    "checksums")
        verify_checksums
        ;;
    "config")
        check_configurations
        ;;
    "report")
        generate_report
        ;;
    "help"|"-h"|"--help")
        echo "Black Duck 离线部署包验证脚本"
        echo ""
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  (无), verify     执行完整验证 (默认)"
        echo "  files            仅检查必需文件"
        echo "  permissions      仅检查脚本权限"
        echo "  images           仅检查镜像文件"
        echo "  checksums        仅验证校验和"
        echo "  config           仅检查配置文件"
        echo "  report           生成验证报告"
        echo "  help             显示此帮助信息"
        echo ""
        ;;
    *)
        error "未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac
