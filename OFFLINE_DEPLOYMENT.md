# Black Duck 离线部署指南

## 📦 概述

Black Duck 离线部署方案允许您在没有网络连接的环境中部署完整的 Black Duck 演示环境。该方案包含所有必需的 Docker 镜像文件和自动化脚本，实现真正的离线部署。

## 🎯 适用场景

- **内网环境**: 无法访问外网的企业内网
- **安全环境**: 对网络访问有严格限制的环境
- **演示环境**: 需要在客户现场进行产品演示
- **开发测试**: 开发和测试环境的快速搭建
- **灾备环境**: 作为备用部署方案

## 📋 离线部署包内容

### 核心文件
```
blackduck-deployment/
├── 📁 docker-images/           # Docker 镜像文件 (总计 ~680MB)
│   ├── postgres-15.tar        # PostgreSQL 15 数据库 (417MB)
│   ├── redis-7-alpine.tar     # Redis 7 缓存服务 (40MB)
│   ├── nginx-alpine.tar       # Nginx 反向代理 (51MB)
│   ├── rabbitmq-3-management-alpine.tar  # RabbitMQ 消息队列 (172MB)
│   ├── checksums.sha256        # SHA256 校验文件
│   └── images-info.txt         # 镜像详细信息
├── 🔧 load-images.sh           # 镜像加载脚本
├── 🐳 docker-compose-offline.yml  # 离线部署配置
├── ⚙️ configs/nginx-offline.conf  # 离线 Nginx 配置
└── 🌐 demo/offline.html        # 离线部署验证页面
```

### 支持脚本
- `deploy.sh` - 支持离线部署模式
- `health-check.sh` - 健康检查脚本
- `setup.sh` - 环境初始化脚本
- `scripts/backup.sh` - 数据备份脚本
- `scripts/restore.sh` - 数据恢复脚本

## 🚀 快速开始

### 步骤 1: 准备离线部署包

在有网络连接的环境中：

```bash
# 1. 克隆或下载项目
git clone <repository-url> blackduck-deployment
cd blackduck-deployment

# 2. 初始化环境
./setup.sh

# 3. 在线部署一次以拉取镜像
./deploy.sh

# 4. 导出镜像文件 (镜像已在前面的部署中拉取)
# 镜像文件已自动导出到 docker-images/ 目录

# 5. 验证离线部署包
./load-images.sh verify
```

### 步骤 2: 传输到目标环境

```bash
# 将整个 blackduck-deployment 目录复制到目标服务器
# 可以使用以下方式之一:

# 方式1: 使用 scp
scp -r blackduck-deployment/ user@target-server:/opt/

# 方式2: 创建压缩包
tar czf blackduck-offline.tar.gz blackduck-deployment/
# 然后传输 blackduck-offline.tar.gz 到目标服务器并解压

# 方式3: 使用 USB 设备或其他离线介质
```

### 步骤 3: 在目标服务器上部署

```bash
# 1. 进入部署目录
cd /opt/blackduck-deployment

# 2. 设置脚本权限
chmod +x *.sh scripts/*.sh

# 3. 加载 Docker 镜像
./load-images.sh

# 4. 离线部署
./deploy.sh offline

# 5. 验证部署
curl http://localhost/offline
```

## 🔧 详细操作指南

### 镜像管理

```bash
# 加载所有镜像
./load-images.sh

# 验证镜像完整性
./load-images.sh verify

# 列出已加载的镜像
./load-images.sh list

# 清理未使用的镜像
./load-images.sh cleanup

# 查看帮助
./load-images.sh help
```

### 服务管理

```bash
# 离线部署
./deploy.sh offline

# 停止离线服务
./deploy.sh stop offline

# 重启离线服务
./deploy.sh restart offline

# 查看服务状态
./deploy.sh status offline

# 查看服务日志
./deploy.sh logs offline

# 查看特定服务日志
./deploy.sh logs offline postgres
```

### 健康检查

```bash
# 完整健康检查
./health-check.sh

# 检查特定组件
./health-check.sh database
./health-check.sh redis
./health-check.sh network
```

## 🌐 访问信息

部署成功后，可以通过以下地址访问：

| 服务 | 访问地址 | 说明 |
|------|----------|------|
| **离线部署页面** | http://localhost/offline | 离线部署验证和状态页面 |
| **主页面** | http://localhost/ | 演示环境主页 |
| **健康检查** | http://localhost/health | 返回 "healthy - offline deployment" |
| **系统状态** | http://localhost/status | 系统状态监控页面 |
| **RabbitMQ 管理** | http://localhost:15672 | 用户名/密码: guest/guest |

## 📊 系统要求

### 最低要求
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, RHEL 7+)
- **Docker**: Docker Engine 20.10+
- **内存**: 4GB+ RAM
- **CPU**: 2+ 核心
- **磁盘空间**: 2GB+ 可用空间

### 推荐配置
- **内存**: 8GB+ RAM
- **CPU**: 4+ 核心
- **磁盘空间**: 5GB+ 可用空间
- **存储**: SSD 存储 (提升性能)

### 网络要求
- **离线环境**: 无需网络连接
- **端口要求**: 80, 443, 15672 (可配置)

## 🔍 验证和测试

### 部署验证清单

- [ ] 所有 Docker 镜像成功加载
- [ ] 所有服务容器正常启动
- [ ] 健康检查端点响应正常
- [ ] Web 界面可以访问
- [ ] 数据库连接正常
- [ ] Redis 缓存服务正常
- [ ] RabbitMQ 管理界面可访问

### 测试命令

```bash
# 1. 验证镜像加载
./load-images.sh list

# 2. 检查服务状态
./deploy.sh status offline

# 3. 测试网络连接
curl http://localhost/health
curl http://localhost/offline

# 4. 验证数据库
./health-check.sh database

# 5. 验证缓存服务
./health-check.sh redis
```

## 🚨 故障排除

### 常见问题

#### 1. 镜像加载失败
```bash
# 问题: 镜像文件损坏或不完整
# 解决方案:
./load-images.sh verify  # 验证文件完整性
df -h                    # 检查磁盘空间
docker system prune      # 清理 Docker 空间
```

#### 2. 服务启动失败
```bash
# 问题: 资源不足或端口冲突
# 解决方案:
./deploy.sh logs offline        # 查看错误日志
netstat -tlnp | grep :80       # 检查端口占用
free -h                        # 检查内存使用
```

#### 3. 网络访问问题
```bash
# 问题: 无法访问 Web 界面
# 解决方案:
./deploy.sh status offline     # 检查服务状态
docker ps | grep blackduck     # 检查容器状态
curl http://localhost/health   # 测试健康检查
```

#### 4. 权限问题
```bash
# 问题: 脚本无法执行
# 解决方案:
chmod +x *.sh scripts/*.sh     # 设置执行权限
sudo chown -R $USER:$USER .    # 修改文件所有者
```

### 日志分析

```bash
# 查看所有服务日志
./deploy.sh logs offline

# 查看特定服务日志
./deploy.sh logs offline postgres
./deploy.sh logs offline nginx
./deploy.sh logs offline redis

# 查看 Docker 系统日志
docker system events
```

## 📈 性能优化

### 资源调优

```bash
# 编辑 .env 文件调整资源限制
vim .env

# 示例配置 (根据实际硬件调整)
POSTGRES_MEMORY_LIMIT=2G
WEBAPP_MEMORY_LIMIT=4G
REDIS_MEMORY_LIMIT=512M
```

### 存储优化

```bash
# 使用 SSD 存储提升性能
# 定期清理 Docker 资源
docker system prune -a

# 监控磁盘使用
docker system df
du -sh docker-images/
```

## 🔄 升级和维护

### 镜像更新

```bash
# 在有网络的环境中更新镜像
docker pull postgres:15
docker pull redis:7-alpine
docker pull nginx:alpine
docker pull rabbitmq:3-management-alpine

# 重新导出镜像
docker save postgres:15 -o docker-images/postgres-15.tar
# ... 其他镜像
```

### 数据备份

```bash
# 备份数据
./scripts/backup.sh

# 恢复数据
./scripts/restore.sh
```

## 📞 技术支持

### 获取帮助

```bash
# 查看脚本帮助
./load-images.sh help
./deploy.sh help
./health-check.sh help
```

### 联系方式

- **项目文档**: README.md
- **快速指南**: QUICK_START.md
- **部署总结**: DEPLOYMENT_SUMMARY.md

---

## ✅ 离线部署成功标志

当您看到以下信息时，表示离线部署成功：

1. **镜像加载成功**: `./load-images.sh list` 显示所有镜像
2. **服务运行正常**: `./deploy.sh status offline` 显示所有服务健康
3. **网络访问正常**: `curl http://localhost/offline` 返回离线部署页面
4. **健康检查通过**: `./health-check.sh` 所有检查项通过

**🎉 恭喜！您已成功完成 Black Duck 离线部署！**
