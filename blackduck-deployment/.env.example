# Black Duck Docker Compose Environment Configuration
# Copy this file to .env and modify the values as needed

# =============================================================================
# Black Duck Version
# =============================================================================
BLACKDUCK_VERSION=2025.4.1

# =============================================================================
# Database Configuration
# =============================================================================
POSTGRES_DB=bds_hub
POSTGRES_USER=blackduck_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_ADMIN_USER=blackduck
POSTGRES_ADMIN_PASSWORD=your_admin_password_here

# =============================================================================
# RabbitMQ Configuration
# =============================================================================
RABBITMQ_USER=blackduck
RABBITMQ_PASSWORD=your_rabbitmq_password_here

# =============================================================================
# Public Access Configuration
# =============================================================================
PUBLIC_HUB_HOST=localhost
PUBLIC_HUB_PORT=443
HTTP_PORT=80
HTTPS_PORT=443

# =============================================================================
# Resource Limits - Test Environment
# =============================================================================
# PostgreSQL Resources
POSTGRES_MEMORY_LIMIT=4G
POSTGRES_CPU_LIMIT=2.0
POSTGRES_MEMORY_RESERVATION=2G
POSTGRES_CPU_RESERVATION=1.0

# Redis Resources
REDIS_MEMORY_LIMIT=1G
REDIS_CPU_LIMIT=0.5

# RabbitMQ Resources
RABBITMQ_MEMORY_LIMIT=1G
RABBITMQ_CPU_LIMIT=0.5

# Authentication Service Resources
AUTH_MEMORY_LIMIT=2G
AUTH_CPU_LIMIT=1.0

# Web Application Resources
WEBAPP_MEMORY_LIMIT=8G
WEBAPP_CPU_LIMIT=4.0
WEBAPP_MEMORY_RESERVATION=4G
WEBAPP_CPU_RESERVATION=2.0
WEBAPP_MAX_MEMORY=8192m

# Scan Service Resources
SCAN_MEMORY_LIMIT=4G
SCAN_CPU_LIMIT=2.0
SCAN_MAX_MEMORY=4096m

# Job Runner Resources
JOBRUNNER_MEMORY_LIMIT=4G
JOBRUNNER_CPU_LIMIT=2.0
JOBRUNNER_MAX_MEMORY=4096m

# BOM Engine Resources
BOMENGINE_MEMORY_LIMIT=4G
BOMENGINE_CPU_LIMIT=2.0
BOMENGINE_MAX_MEMORY=4096m

# Match Engine Resources
MATCHENGINE_MEMORY_LIMIT=4G
MATCHENGINE_CPU_LIMIT=2.0
MATCHENGINE_MAX_MEMORY=4096m

# Nginx Resources
NGINX_MEMORY_LIMIT=512M
NGINX_CPU_LIMIT=0.5

# Logstash Resources (Optional)
LOGSTASH_MEMORY_LIMIT=2G
LOGSTASH_CPU_LIMIT=1.0

# =============================================================================
# Production Environment Resource Limits (Uncomment and adjust for production)
# =============================================================================
# POSTGRES_MEMORY_LIMIT=16G
# POSTGRES_CPU_LIMIT=8.0
# POSTGRES_MEMORY_RESERVATION=8G
# POSTGRES_CPU_RESERVATION=4.0

# WEBAPP_MEMORY_LIMIT=16G
# WEBAPP_CPU_LIMIT=8.0
# WEBAPP_MEMORY_RESERVATION=8G
# WEBAPP_CPU_RESERVATION=4.0
# WEBAPP_MAX_MEMORY=16384m

# SCAN_MEMORY_LIMIT=8G
# SCAN_CPU_LIMIT=4.0
# SCAN_MAX_MEMORY=8192m

# JOBRUNNER_MEMORY_LIMIT=8G
# JOBRUNNER_CPU_LIMIT=4.0
# JOBRUNNER_MAX_MEMORY=8192m

# BOMENGINE_MEMORY_LIMIT=8G
# BOMENGINE_CPU_LIMIT=4.0
# BOMENGINE_MAX_MEMORY=8192m

# MATCHENGINE_MEMORY_LIMIT=8G
# MATCHENGINE_CPU_LIMIT=4.0
# MATCHENGINE_MAX_MEMORY=8192m

# =============================================================================
# SSL/TLS Configuration (for production)
# =============================================================================
# SSL_CERT_PATH=/path/to/your/certificate.crt
# SSL_KEY_PATH=/path/to/your/private.key
# SSL_CA_PATH=/path/to/your/ca-bundle.crt

# =============================================================================
# Backup Configuration
# =============================================================================
# BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
# BACKUP_RETENTION_DAYS=30
# BACKUP_PATH=/opt/blackduck/backups

# =============================================================================
# Monitoring Configuration
# =============================================================================
# ENABLE_MONITORING=true
# PROMETHEUS_PORT=9090
# GRAFANA_PORT=3000
