Black Duck 离线部署包验证报告
生成时间: 2025年 7月31日 星期四 01时31分36秒 CST
========================================

部署包信息:
- 位置: /Users/<USER>/Desktop/BlackDuck/blackduck-deployment
- 总大小: 701M

镜像文件:
-rw-------  1 <USER>  <GROUP>    51M  7 31 01:11 /Users/<USER>/Desktop/BlackDuck/blackduck-deployment/docker-images/nginx-alpine.tar
-rw-------  1 <USER>  <GROUP>   417M  7 31 01:09 /Users/<USER>/Desktop/BlackDuck/blackduck-deployment/docker-images/postgres-15.tar
-rw-------  1 <USER>  <GROUP>   172M  7 31 01:12 /Users/<USER>/Desktop/BlackDuck/blackduck-deployment/docker-images/rabbitmq-3-management-alpine.tar
-rw-------  1 <USER>  <GROUP>    40M  7 31 01:10 /Users/<USER>/Desktop/BlackDuck/blackduck-deployment/docker-images/redis-7-alpine.tar

配置文件:
- docker-compose-offline.yml: 存在
- .env: 存在
- nginx-offline.conf: 存在

脚本文件:
- load-images.sh: 可执行
- deploy.sh: 可执行
- health-check.sh: 可执行

文档文件:
- README.md: 存在
- OFFLINE_DEPLOYMENT.md: 缺失

