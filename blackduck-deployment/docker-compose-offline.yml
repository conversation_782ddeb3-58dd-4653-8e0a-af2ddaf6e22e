version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: blackduck-postgres
    hostname: postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-bds_hub}
      - POSTGRES_USER=${POSTGRES_USER:-blackduck_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-BlackDuck2025!}
      - POSTGRES_HOST_AUTH_METHOD=trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./volumes/logs/postgres:/var/log/postgresql
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-blackduck_user} -d ${POSTGRES_DB:-bds_hub}"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT:-4G}
          cpus: '${POSTGRES_CPU_LIMIT:-2.0}'
        reservations:
          memory: ${POSTGRES_MEMORY_RESERVATION:-2G}
          cpus: '${POSTGRES_CPU_RESERVATION:-1.0}'

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: blackduck-redis
    hostname: redis
    volumes:
      - redis_data:/data
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT:-1G}
          cpus: '${REDIS_CPU_LIMIT:-0.5}'

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: blackduck-rabbitmq
    hostname: rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-blackduck}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:-RabbitMQ2025!}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "15672:15672"  # Management UI
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: ${RABBITMQ_MEMORY_LIMIT:-1G}
          cpus: '${RABBITMQ_CPU_LIMIT:-0.5}'

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: blackduck-nginx
    hostname: nginx
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - demo-webapp
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./configs/nginx-offline.conf:/etc/nginx/nginx.conf:ro
      - ./volumes/logs/nginx:/var/log/nginx
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: ${NGINX_MEMORY_LIMIT:-512M}
          cpus: '${NGINX_CPU_LIMIT:-0.5}'

  # Demo Web Application
  demo-webapp:
    image: nginx:alpine
    container_name: blackduck-demo-webapp
    hostname: demo-webapp
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./demo/index.html:/usr/share/nginx/html/index.html:ro
      - ./demo/status.html:/usr/share/nginx/html/status.html:ro
      - ./demo/offline.html:/usr/share/nginx/html/offline.html:ro
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local

networks:
  blackduck-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
