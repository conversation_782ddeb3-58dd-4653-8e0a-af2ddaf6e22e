#!/bin/bash

# Black Duck Setup Script
# Author: Deployment Automation
# Version: 1.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Print banner
print_banner() {
    echo ""
    echo "========================================"
    echo "  Black Duck Docker Compose 部署设置"
    echo "========================================"
    echo ""
}

# Check system requirements
check_system_requirements() {
    log "检查系统要求..."
    
    local errors=0
    
    # Check operating system
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        error "不支持的操作系统: $OSTYPE"
        error "Black Duck 需要 Linux 操作系统"
        ((errors++))
    else
        success "操作系统检查通过: Linux"
    fi
    
    # Check memory
    local total_mem=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$total_mem" -lt 16 ]; then
        error "内存不足: ${total_mem}GB (最少需要 16GB)"
        ((errors++))
    else
        success "内存检查通过: ${total_mem}GB"
    fi
    
    # Check disk space
    local available_space=$(df -BG "$SCRIPT_DIR" | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$available_space" -lt 100 ]; then
        error "磁盘空间不足: ${available_space}GB (最少需要 100GB)"
        ((errors++))
    else
        success "磁盘空间检查通过: ${available_space}GB 可用"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker 未安装"
        echo "请访问 https://docs.docker.com/engine/install/ 安装 Docker"
        ((errors++))
    else
        local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        success "Docker 已安装: $docker_version"
        
        # Check if Docker daemon is running
        if ! docker info &> /dev/null; then
            error "Docker 守护进程未运行"
            echo "请启动 Docker 服务: sudo systemctl start docker"
            ((errors++))
        else
            success "Docker 守护进程运行正常"
        fi
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        error "Docker Compose 未安装"
        echo "请安装 Docker Compose 或使用 Docker Compose V2"
        ((errors++))
    else
        if command -v docker-compose &> /dev/null; then
            local compose_version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            success "Docker Compose 已安装: $compose_version"
        else
            success "Docker Compose V2 已安装"
        fi
    fi
    
    # Check required tools
    local tools=("curl" "tar" "gzip" "bc")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error "缺少必需工具: $tool"
            ((errors++))
        else
            success "工具检查通过: $tool"
        fi
    done
    
    if [ $errors -gt 0 ]; then
        error "系统要求检查失败，发现 $errors 个问题"
        return 1
    else
        success "系统要求检查通过"
        return 0
    fi
}

# Set file permissions
set_permissions() {
    log "设置文件权限..."
    
    # Make scripts executable
    chmod +x "$SCRIPT_DIR/deploy.sh"
    chmod +x "$SCRIPT_DIR/health-check.sh"
    chmod +x "$SCRIPT_DIR/scripts/backup.sh"
    chmod +x "$SCRIPT_DIR/scripts/restore.sh"
    
    success "脚本执行权限设置完成"
}

# Create directory structure
create_directories() {
    log "创建目录结构..."
    
    # Create volume directories
    mkdir -p "$SCRIPT_DIR/volumes/logs/postgres"
    mkdir -p "$SCRIPT_DIR/volumes/logs/webapp"
    mkdir -p "$SCRIPT_DIR/volumes/logs/authentication"
    mkdir -p "$SCRIPT_DIR/volumes/logs/scan"
    mkdir -p "$SCRIPT_DIR/volumes/logs/jobrunner"
    mkdir -p "$SCRIPT_DIR/volumes/logs/bomengine"
    mkdir -p "$SCRIPT_DIR/volumes/logs/matchengine"
    mkdir -p "$SCRIPT_DIR/volumes/logs/nginx"
    mkdir -p "$SCRIPT_DIR/volumes/uploads"
    mkdir -p "$SCRIPT_DIR/volumes/certs"
    
    # Create backup directory
    mkdir -p "$SCRIPT_DIR/backups"
    
    # Set proper permissions
    chmod -R 755 "$SCRIPT_DIR/volumes"
    chmod -R 755 "$SCRIPT_DIR/backups"
    
    success "目录结构创建完成"
}

# Setup environment file
setup_environment() {
    log "设置环境配置文件..."
    
    if [ ! -f "$SCRIPT_DIR/.env" ]; then
        if [ -f "$SCRIPT_DIR/.env.example" ]; then
            cp "$SCRIPT_DIR/.env.example" "$SCRIPT_DIR/.env"
            success "环境配置文件已创建"
            
            warning "请编辑 .env 文件并修改以下重要配置："
            warning "- POSTGRES_PASSWORD (数据库密码)"
            warning "- POSTGRES_ADMIN_PASSWORD (管理员密码)"
            warning "- RABBITMQ_PASSWORD (消息队列密码)"
            warning "- PUBLIC_HUB_HOST (公共访问地址)"
        else
            error ".env.example 文件不存在"
            return 1
        fi
    else
        warning ".env 文件已存在，跳过创建"
    fi
}

# Generate self-signed SSL certificate
generate_ssl_certificate() {
    log "生成自签名 SSL 证书（用于测试环境）..."
    
    local cert_dir="$SCRIPT_DIR/volumes/certs"
    local cert_file="$cert_dir/blackduck.crt"
    local key_file="$cert_dir/blackduck.key"
    
    if [ ! -f "$cert_file" ] || [ ! -f "$key_file" ]; then
        # Generate certificate
        openssl req -x509 -newkey rsa:4096 -keyout "$key_file" -out "$cert_file" \
            -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost" \
            2>/dev/null
        
        if [ $? -eq 0 ]; then
            success "SSL 证书生成完成"
            warning "这是自签名证书，仅适用于测试环境"
            warning "生产环境请使用正式的 SSL 证书"
        else
            error "SSL 证书生成失败"
            return 1
        fi
    else
        warning "SSL 证书已存在，跳过生成"
    fi
}

# Check network ports
check_network_ports() {
    log "检查网络端口..."
    
    local ports=(80 443)
    local port_conflicts=()
    
    for port in "${ports[@]}"; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            port_conflicts+=("$port")
        fi
    done
    
    if [ ${#port_conflicts[@]} -gt 0 ]; then
        warning "以下端口已被占用: ${port_conflicts[*]}"
        warning "请确保这些端口可用，或修改 .env 文件中的端口配置"
    else
        success "网络端口检查通过"
    fi
}

# Pull Docker images
pull_docker_images() {
    log "拉取 Docker 镜像（可选）..."
    
    read -p "是否现在拉取 Docker 镜像？这可能需要几分钟时间 (y/N): " pull_images
    
    if [[ "$pull_images" =~ ^[Yy]$ ]]; then
        log "开始拉取 Docker 镜像..."
        
        cd "$SCRIPT_DIR"
        if command -v docker-compose &> /dev/null; then
            docker-compose pull
        else
            docker compose pull
        fi
        
        success "Docker 镜像拉取完成"
    else
        log "跳过 Docker 镜像拉取"
    fi
}

# Show setup summary
show_setup_summary() {
    echo ""
    echo "========================================"
    echo "设置完成总结"
    echo "========================================"
    echo ""
    echo "✅ 系统要求检查通过"
    echo "✅ 文件权限设置完成"
    echo "✅ 目录结构创建完成"
    echo "✅ 环境配置文件准备完成"
    echo "✅ SSL 证书生成完成"
    echo ""
    echo "下一步操作："
    echo "1. 编辑 .env 文件，修改密码和配置"
    echo "2. 运行 ./deploy.sh 开始部署"
    echo "3. 运行 ./health-check.sh 验证部署"
    echo ""
    echo "快速命令："
    echo "  vim .env              # 编辑环境配置"
    echo "  ./deploy.sh           # 开始部署"
    echo "  ./health-check.sh     # 健康检查"
    echo ""
    echo "访问地址："
    echo "  https://localhost:443"
    echo ""
    echo "默认登录信息："
    echo "  用户名: sysadmin"
    echo "  密码: blackduck"
    echo ""
    warning "重要提示："
    warning "1. 请务必修改 .env 文件中的默认密码"
    warning "2. 首次部署可能需要 10-15 分钟"
    warning "3. 生产环境请使用正式的 SSL 证书"
    echo ""
}

# Main setup function
main() {
    print_banner
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        warning "不建议以 root 用户运行此脚本"
        warning "建议使用普通用户并确保该用户在 docker 组中"
        echo ""
        read -p "是否继续? (y/N): " continue_as_root
        if [[ ! "$continue_as_root" =~ ^[Yy]$ ]]; then
            log "设置已取消"
            exit 0
        fi
    fi
    
    # Run setup steps
    if ! check_system_requirements; then
        error "系统要求检查失败，请解决上述问题后重新运行"
        exit 1
    fi
    
    set_permissions
    create_directories
    setup_environment
    generate_ssl_certificate
    check_network_ports
    pull_docker_images
    show_setup_summary
    
    success "Black Duck 部署环境设置完成！"
}

# Handle script arguments
case "${1:-}" in
    ""|"setup")
        main
        ;;
    "check")
        check_system_requirements
        ;;
    "permissions")
        set_permissions
        ;;
    "directories")
        create_directories
        ;;
    "ssl")
        generate_ssl_certificate
        ;;
    "help"|"-h"|"--help")
        echo "Black Duck 设置脚本"
        echo ""
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  (无), setup      执行完整设置 (默认)"
        echo "  check            仅检查系统要求"
        echo "  permissions      仅设置文件权限"
        echo "  directories      仅创建目录结构"
        echo "  ssl              仅生成 SSL 证书"
        echo "  help             显示此帮助信息"
        echo ""
        ;;
    *)
        error "未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac
