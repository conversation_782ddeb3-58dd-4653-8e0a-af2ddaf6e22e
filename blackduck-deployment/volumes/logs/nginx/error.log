2025/07/30 16:52:36 [emerg] 1#1: host not found in upstream "demo-webapp:80" in /etc/nginx/nginx.conf:35
2025/07/30 16:52:45 [emerg] 1#1: host not found in upstream "demo-webapp:80" in /etc/nginx/nginx.conf:35
2025/07/30 16:52:49 [emerg] 1#1: host not found in upstream "demo-webapp:80" in /etc/nginx/nginx.conf:35
2025/07/30 16:52:57 [emerg] 1#1: host not found in upstream "demo-webapp:80" in /etc/nginx/nginx.conf:35
2025/07/30 16:53:04 [emerg] 1#1: host not found in upstream "demo-webapp:80" in /etc/nginx/nginx.conf:35
2025/07/30 16:53:10 [emerg] 1#1: host not found in upstream "demo-webapp:80" in /etc/nginx/nginx.conf:35
2025/07/30 16:53:20 [emerg] 1#1: host not found in upstream "demo-webapp:80" in /etc/nginx/nginx.conf:35
2025/07/30 16:53:31 [emerg] 1#1: host not found in upstream "demo-webapp:80" in /etc/nginx/nginx.conf:35
2025/07/30 16:53:48 [emerg] 1#1: cannot load certificate "/etc/ssl/certs/ssl-cert-snakeoil.pem": BIO_new_file() failed (SSL: error:80000002:system library::No such file or directory:calling fopen(/etc/ssl/certs/ssl-cert-snakeoil.pem, r) error:10000080:BIO routines::no such file)
2025/07/30 16:54:17 [emerg] 1#1: cannot load certificate "/etc/ssl/certs/ssl-cert-snakeoil.pem": BIO_new_file() failed (SSL: error:80000002:system library::No such file or directory:calling fopen(/etc/ssl/certs/ssl-cert-snakeoil.pem, r) error:10000080:BIO routines::no such file)
