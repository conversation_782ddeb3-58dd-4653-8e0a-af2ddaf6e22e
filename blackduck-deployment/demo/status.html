<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Duck 系统状态</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .status-item.warning {
            border-left-color: #ffc107;
        }
        .status-item.error {
            border-left-color: #dc3545;
        }
        .status-name {
            font-weight: bold;
        }
        .status-value {
            color: #666;
        }
        .timestamp {
            text-align: center;
            margin-top: 30px;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Black Duck 系统状态</h1>
            <p>演示环境 - 实时状态监控</p>
        </div>

        <div class="status-item">
            <div class="status-name">🐘 PostgreSQL 数据库</div>
            <div class="status-value">运行正常</div>
        </div>

        <div class="status-item">
            <div class="status-name">⚡ Redis 缓存服务</div>
            <div class="status-value">运行正常</div>
        </div>

        <div class="status-item">
            <div class="status-name">🐰 RabbitMQ 消息队列</div>
            <div class="status-value">运行正常</div>
        </div>

        <div class="status-item">
            <div class="status-name">🌐 Nginx 反向代理</div>
            <div class="status-value">运行正常</div>
        </div>

        <div class="status-item">
            <div class="status-name">🔗 网络连接</div>
            <div class="status-value">blackduck-net 正常</div>
        </div>

        <div class="status-item">
            <div class="status-name">💾 数据卷</div>
            <div class="status-value">持久化存储正常</div>
        </div>

        <div class="status-item warning">
            <div class="status-name">🛡️ Black Duck 应用服务</div>
            <div class="status-value">需要许可证 (演示模式)</div>
        </div>

        <div class="status-item">
            <div class="status-name">📊 系统资源</div>
            <div class="status-value">内存和CPU使用正常</div>
        </div>

        <div class="timestamp">
            最后更新时间: <span id="updateTime"></span>
        </div>
    </div>

    <script>
        function updateTime() {
            document.getElementById('updateTime').textContent = new Date().toLocaleString('zh-CN');
        }
        
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
