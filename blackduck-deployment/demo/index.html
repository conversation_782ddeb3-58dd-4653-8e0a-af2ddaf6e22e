<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Duck 部署验证 - 演示环境</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .status-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .status-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status-description {
            opacity: 0.8;
            line-height: 1.5;
        }
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }
        .info-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffd700;
        }
        .info-list {
            list-style: none;
            padding: 0;
        }
        .info-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .info-list li:last-child {
            border-bottom: none;
        }
        .success { color: #4CAF50; }
        .warning { color: #FF9800; }
        .info { color: #2196F3; }
        .footer {
            text-align: center;
            margin-top: 40px;
            opacity: 0.7;
            font-size: 0.9em;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️ Black Duck</div>
            <div class="subtitle">Docker Compose 部署验证 - 演示环境</div>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <div class="status-icon success">✅</div>
                <div class="status-title">部署架构验证</div>
                <div class="status-description">
                    Docker Compose 配置文件语法正确，服务依赖关系配置合理，网络和存储配置完整。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon success">🐘</div>
                <div class="status-title">PostgreSQL 数据库</div>
                <div class="status-description">
                    使用 PostgreSQL 15 镜像替代官方镜像，数据库服务正常启动，支持健康检查。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon success">⚡</div>
                <div class="status-title">Redis 缓存</div>
                <div class="status-description">
                    使用 Redis 7 Alpine 镜像，缓存服务运行正常，支持数据持久化。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon success">🐰</div>
                <div class="status-title">RabbitMQ 消息队列</div>
                <div class="status-description">
                    使用 RabbitMQ 3 Management 镜像，消息队列服务正常，管理界面可访问。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon success">🌐</div>
                <div class="status-title">Nginx 反向代理</div>
                <div class="status-description">
                    使用 Nginx Alpine 镜像，反向代理配置正确，支持 HTTP/HTTPS 访问。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon warning">⚠️</div>
                <div class="status-title">Black Duck 应用服务</div>
                <div class="status-description">
                    官方镜像需要许可证，当前使用演示配置验证部署架构和基础设施服务。
                </div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">🔗 访问信息</div>
            <ul class="info-list">
                <li><strong>Web 界面:</strong> <span class="info">http://localhost:80</span> 或 <span class="info">https://localhost:443</span></li>
                <li><strong>RabbitMQ 管理界面:</strong> <span class="info">http://localhost:15672</span></li>
                <li><strong>健康检查端点:</strong> <span class="info">http://localhost/health</span></li>
                <li><strong>状态页面:</strong> <span class="info">http://localhost/status</span></li>
            </ul>
        </div>

        <div class="info-section">
            <div class="info-title">📊 部署验证结果</div>
            <ul class="info-list">
                <li><span class="success">✅ Docker Compose 配置文件语法正确</span></li>
                <li><span class="success">✅ 环境变量配置完整</span></li>
                <li><span class="success">✅ 网络配置正确 (blackduck-net)</span></li>
                <li><span class="success">✅ 数据卷配置正确</span></li>
                <li><span class="success">✅ 服务依赖关系合理</span></li>
                <li><span class="success">✅ 健康检查配置完整</span></li>
                <li><span class="success">✅ 资源限制配置合理</span></li>
                <li><span class="warning">⚠️ 官方镜像需要许可证</span></li>
            </ul>
        </div>

        <div class="info-section">
            <div class="info-title">🛠️ 管理命令</div>
            <div style="text-align: center;">
                <a href="#" class="btn" onclick="alert('运行: ./health-check.sh')">健康检查</a>
                <a href="#" class="btn" onclick="alert('运行: docker compose ps')">查看状态</a>
                <a href="#" class="btn" onclick="alert('运行: docker compose logs')">查看日志</a>
                <a href="#" class="btn" onclick="alert('运行: ./scripts/backup.sh')">数据备份</a>
            </div>
        </div>

        <div class="footer">
            <p>Black Duck Docker Compose 部署方案验证 - macOS 环境</p>
            <p>部署时间: <span id="deployTime"></span></p>
        </div>
    </div>

    <script>
        document.getElementById('deployTime').textContent = new Date().toLocaleString('zh-CN');
        
        // 简单的状态检查动画
        setInterval(() => {
            const cards = document.querySelectorAll('.status-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        card.style.transform = 'scale(1)';
                    }, 200);
                }, index * 100);
            });
        }, 5000);
    </script>
</body>
</html>
