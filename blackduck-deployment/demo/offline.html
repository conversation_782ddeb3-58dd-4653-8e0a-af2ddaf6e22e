<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Duck 离线部署 - 验证成功</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .offline-badge {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            margin-top: 10px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .status-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .status-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status-description {
            opacity: 0.8;
            line-height: 1.5;
        }
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }
        .info-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #f39c12;
        }
        .info-list {
            list-style: none;
            padding: 0;
        }
        .info-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .info-list li:last-child {
            border-bottom: none;
        }
        .success { color: #27ae60; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        .offline { color: #e74c3c; }
        .footer {
            text-align: center;
            margin-top: 40px;
            opacity: 0.7;
            font-size: 0.9em;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .deployment-info {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️ Black Duck</div>
            <div class="subtitle">离线部署验证环境</div>
            <div class="offline-badge">📡 离线模式</div>
        </div>

        <div class="deployment-info">
            <h3>🚀 离线部署成功！</h3>
            <p>恭喜！您已成功在无网络连接的环境中部署了 Black Duck 演示环境。所有 Docker 镜像均从本地文件加载，无需互联网连接。</p>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <div class="status-icon success">✅</div>
                <div class="status-title">离线镜像加载</div>
                <div class="status-description">
                    所有 Docker 镜像已从本地 .tar 文件成功加载，总大小约 680MB。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon success">🐘</div>
                <div class="status-title">PostgreSQL 数据库</div>
                <div class="status-description">
                    PostgreSQL 15 数据库服务正常运行，支持完整的数据持久化功能。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon success">⚡</div>
                <div class="status-title">Redis 缓存</div>
                <div class="status-description">
                    Redis 7 Alpine 缓存服务运行正常，提供高性能数据缓存支持。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon warning">🐰</div>
                <div class="status-title">RabbitMQ 消息队列</div>
                <div class="status-description">
                    RabbitMQ 3 Management 消息队列服务，管理界面可通过端口 15672 访问。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon success">🌐</div>
                <div class="status-title">Nginx 反向代理</div>
                <div class="status-description">
                    Nginx Alpine 反向代理正常运行，提供 HTTP 访问和负载均衡功能。
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon offline">📡</div>
                <div class="status-title">离线部署模式</div>
                <div class="status-description">
                    当前运行在完全离线模式，所有组件均从本地镜像启动，无需网络连接。
                </div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">🔗 访问信息</div>
            <ul class="info-list">
                <li><strong>主 Web 界面:</strong> <span class="info">http://localhost:80</span></li>
                <li><strong>离线部署页面:</strong> <span class="info">http://localhost/offline</span></li>
                <li><strong>系统状态页面:</strong> <span class="info">http://localhost/status</span></li>
                <li><strong>健康检查端点:</strong> <span class="info">http://localhost/health</span></li>
                <li><strong>RabbitMQ 管理界面:</strong> <span class="info">http://localhost:15672</span> (guest/guest)</li>
            </ul>
        </div>

        <div class="info-section">
            <div class="info-title">📦 离线部署特性</div>
            <ul class="info-list">
                <li><span class="success">✅ 无需网络连接</span> - 所有镜像从本地加载</li>
                <li><span class="success">✅ 完整功能验证</span> - 数据库、缓存、消息队列全部可用</li>
                <li><span class="success">✅ 镜像完整性验证</span> - SHA256 校验确保文件完整</li>
                <li><span class="success">✅ 自动化部署</span> - 一键加载和部署脚本</li>
                <li><span class="success">✅ 跨平台支持</span> - 支持各种 Linux 发行版</li>
                <li><span class="info">ℹ️ 演示环境</span> - 适用于概念验证和功能演示</li>
            </ul>
        </div>

        <div class="info-section">
            <div class="info-title">🛠️ 管理命令</div>
            <div style="text-align: center;">
                <a href="#" class="btn" onclick="alert('运行: docker compose -f docker-compose-offline.yml ps')">查看状态</a>
                <a href="#" class="btn" onclick="alert('运行: docker compose -f docker-compose-offline.yml logs')">查看日志</a>
                <a href="#" class="btn" onclick="alert('运行: ./health-check.sh')">健康检查</a>
                <a href="#" class="btn" onclick="alert('运行: docker images')">查看镜像</a>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">📋 镜像信息</div>
            <ul class="info-list">
                <li><strong>postgres:15</strong> - 417MB (PostgreSQL 数据库)</li>
                <li><strong>redis:7-alpine</strong> - 40MB (Redis 缓存)</li>
                <li><strong>nginx:alpine</strong> - 51MB (Nginx 代理)</li>
                <li><strong>rabbitmq:3-management-alpine</strong> - 172MB (消息队列)</li>
                <li><strong>总计大小:</strong> <span class="info">约 680MB</span></li>
            </ul>
        </div>

        <div class="footer">
            <p>Black Duck 离线部署验证环境 - 无网络连接部署</p>
            <p>部署时间: <span id="deployTime"></span></p>
            <p>部署模式: 离线模式 | 镜像来源: 本地文件</p>
        </div>
    </div>

    <script>
        document.getElementById('deployTime').textContent = new Date().toLocaleString('zh-CN');
        
        // 离线模式指示器动画
        setInterval(() => {
            const badge = document.querySelector('.offline-badge');
            badge.style.transform = 'scale(1.05)';
            setTimeout(() => {
                badge.style.transform = 'scale(1)';
            }, 200);
        }, 3000);
    </script>
</body>
</html>
