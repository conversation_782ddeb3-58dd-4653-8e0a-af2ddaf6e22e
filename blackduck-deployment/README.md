# Black Duck Docker Compose 部署方案

这是一个完整的 Black Duck 软件组成分析（SCA）工具的 Docker Compose 部署方案，适用于测试和生产环境。

## 📋 目录结构

```
blackduck-deployment/
├── docker-compose.yml          # 主要的 Docker Compose 配置文件
├── .env                        # 环境变量配置文件
├── .env.example               # 环境变量模板文件
├── deploy.sh                  # 自动化部署脚本
├── health-check.sh            # 健康检查脚本
├── README.md                  # 本文档
├── configs/                   # 配置文件目录
│   ├── nginx.conf            # Nginx 反向代理配置
│   └── logstash.conf         # Logstash 日志处理配置
├── volumes/                   # 数据卷挂载目录
│   ├── logs/                 # 日志文件目录
│   └── uploads/              # 上传文件目录
└── scripts/                   # 辅助脚本目录
    ├── backup.sh             # 数据备份脚本
    └── restore.sh            # 数据恢复脚本
```

## 🚀 快速开始

### 1. 系统要求

**最低要求（测试环境）：**

- CPU: 4 核心
- 内存: 16GB RAM
- 存储: 100GB 可用空间
- 操作系统: Linux (Ubuntu 18.04+, CentOS 7+, RHEL 7+)

**推荐配置（生产环境）：**

- CPU: 8+ 核心
- 内存: 32GB+ RAM
- 存储: 500GB+ SSD
- 网络: 千兆网络连接

**软件依赖：**

- Docker Engine 20.10+
- Docker Compose 1.29+ 或 Docker Compose V2
- curl (用于健康检查)
- bc (用于数值计算)

### 2. 环境准备

```bash
# 克隆或下载部署文件到本地
cd /opt
git clone <repository-url> blackduck-deployment
cd blackduck-deployment

# 设置脚本执行权限
chmod +x deploy.sh health-check.sh scripts/*.sh

# 复制并编辑环境配置文件
cp .env.example .env
vim .env  # 根据需要修改配置
```

### 3. 配置环境变量

编辑 `.env` 文件，重点修改以下配置：

```bash
# 数据库密码（必须修改）
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_ADMIN_PASSWORD=your_admin_password_here

# RabbitMQ 密码（必须修改）
RABBITMQ_PASSWORD=your_rabbitmq_password_here

# 公共访问地址
PUBLIC_HUB_HOST=your-domain.com  # 或 localhost
PUBLIC_HUB_PORT=443

# 端口配置
HTTP_PORT=80
HTTPS_PORT=443
```

### 4. 部署服务

```bash
# 执行自动化部署
./deploy.sh

# 或者手动部署
docker-compose up -d
```

### 5. 验证部署

```bash
# 运行健康检查
./health-check.sh

# 查看服务状态
./deploy.sh status

# 查看日志
./deploy.sh logs webapp
```

## 🔧 详细配置说明

### 服务组件

Black Duck 包含以下核心服务组件：

| 服务               | 描述              | 端口        | 资源要求           |
| ------------------ | ----------------- | ----------- | ------------------ |
| **postgres**       | PostgreSQL 数据库 | 5432        | 4GB RAM, 2 CPU     |
| **redis**          | 缓存服务          | 6379        | 1GB RAM, 0.5 CPU   |
| **rabbitmq**       | 消息队列          | 5672, 15672 | 1GB RAM, 0.5 CPU   |
| **authentication** | 认证服务          | 8443        | 2GB RAM, 1 CPU     |
| **webapp**         | Web 应用服务      | 8443        | 8GB RAM, 4 CPU     |
| **scan**           | 扫描引擎          | 8443        | 4GB RAM, 2 CPU     |
| **jobrunner**      | 任务执行器        | 8443        | 4GB RAM, 2 CPU     |
| **bomengine**      | BOM 引擎          | 8443        | 4GB RAM, 2 CPU     |
| **matchengine**    | 匹配引擎          | 8443        | 4GB RAM, 2 CPU     |
| **nginx**          | 反向代理          | 80, 443     | 512MB RAM, 0.5 CPU |

### 网络配置

- **内部网络**: `blackduck-net` (172.20.0.0/16)
- **外部端口**: 80 (HTTP), 443 (HTTPS)
- **服务发现**: 通过 Docker 内置 DNS

### 存储配置

**数据卷**:

- `postgres_data`: PostgreSQL 数据
- `redis_data`: Redis 缓存数据
- `rabbitmq_data`: RabbitMQ 数据
- `*_data`: 各服务的安全证书和配置

**挂载目录**:

- `./volumes/logs/`: 所有服务日志
- `./volumes/uploads/`: 文件上传目录
- `./configs/`: 配置文件目录

## 🛠️ 管理操作

### 服务管理

```bash
# 启动所有服务
./deploy.sh start

# 停止所有服务
./deploy.sh stop

# 重启所有服务
./deploy.sh restart

# 查看服务状态
./deploy.sh status

# 查看特定服务日志
./deploy.sh logs webapp
./deploy.sh logs postgres
```

### 健康检查

```bash
# 完整健康检查
./health-check.sh

# 生成健康报告
./health-check.sh --report

# 检查特定组件
./health-check.sh containers
./health-check.sh database
./health-check.sh network
```

### 数据备份

```bash
# 完整备份
./scripts/backup.sh

# 仅备份数据库
./scripts/backup.sh database

# 仅备份配置文件
./scripts/backup.sh configs

# 列出现有备份
./scripts/backup.sh list

# 清理旧备份
./scripts/backup.sh cleanup
```

## 🔐 安全配置

### SSL/TLS 配置

**测试环境**（使用自签名证书）：

```bash
# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 将证书放置到正确位置
mkdir -p volumes/certs
cp cert.pem volumes/certs/
cp key.pem volumes/certs/
```

**生产环境**（使用正式证书）：

```bash
# 编辑 .env 文件
SSL_CERT_PATH=/path/to/your/certificate.crt
SSL_KEY_PATH=/path/to/your/private.key
SSL_CA_PATH=/path/to/your/ca-bundle.crt
```

### 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 密码安全

1. **修改默认密码**: 编辑 `.env` 文件中的所有密码
2. **使用强密码**: 至少 12 位，包含大小写字母、数字和特殊字符
3. **定期轮换**: 建议每 90 天更换一次密码

## 📊 监控和日志

### 日志管理

**日志位置**:

- 应用日志: `./volumes/logs/[service]/`
- Nginx 日志: `./volumes/logs/nginx/`
- 系统日志: `docker logs [container_name]`

**日志轮转**:

```bash
# 配置 logrotate
sudo vim /etc/logrotate.d/blackduck

/opt/blackduck-deployment/volumes/logs/*/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
```

### 性能监控

**资源使用情况**:

```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
df -h
free -h
```

**监控集成**:

- Prometheus + Grafana
- ELK Stack (Elasticsearch, Logstash, Kibana)
- 自定义监控脚本

## 🚨 故障排除

### 常见问题

**1. 服务启动失败**

```bash
# 检查日志
./deploy.sh logs [service_name]

# 检查资源使用
docker stats
free -h

# 重启服务
docker-compose restart [service_name]
```

**2. 数据库连接失败**

```bash
# 检查数据库状态
docker exec blackduck-postgres pg_isready -U blackduck_user -d bds_hub

# 检查网络连通性
docker exec blackduck-webapp ping postgres

# 重置数据库连接
docker-compose restart postgres webapp
```

**3. Web 界面无法访问**

```bash
# 检查端口占用
netstat -tlnp | grep :443
netstat -tlnp | grep :80

# 检查 Nginx 配置
docker exec blackduck-nginx nginx -t

# 检查防火墙
sudo ufw status
sudo firewall-cmd --list-all
```

**4. 内存不足**

```bash
# 检查内存使用
free -h
docker stats

# 调整资源限制（编辑 .env 文件）
WEBAPP_MEMORY_LIMIT=4G
POSTGRES_MEMORY_LIMIT=2G

# 重启服务
docker-compose up -d
```

**5. 磁盘空间不足**

```bash
# 检查磁盘使用
df -h

# 清理 Docker 资源
docker system prune -a

# 清理日志文件
find ./volumes/logs -name "*.log" -mtime +7 -delete
```

### 调试模式

**启用详细日志**:

```bash
# 编辑 docker-compose.yml，添加环境变量
environment:
  - LOG_LEVEL=DEBUG
  - SPRING_PROFILES_ACTIVE=debug
```

**容器内调试**:

```bash
# 进入容器
docker exec -it blackduck-webapp bash

# 查看进程
ps aux

# 查看网络连接
netstat -tlnp

# 查看文件系统
df -h
ls -la /opt/blackduck/hub/
```

## 🔄 升级和维护

### 版本升级

```bash
# 1. 备份当前数据
./scripts/backup.sh

# 2. 停止服务
./deploy.sh stop

# 3. 更新版本号（编辑 .env 文件）
BLACKDUCK_VERSION=2025.5.0

# 4. 拉取新镜像
docker-compose pull

# 5. 启动服务
./deploy.sh start

# 6. 验证升级
./health-check.sh
```

### 定期维护

**每日任务**:

- 检查服务状态
- 监控资源使用
- 查看错误日志

**每周任务**:

- 执行完整备份
- 清理旧日志文件
- 更新安全补丁

**每月任务**:

- 检查磁盘使用情况
- 优化数据库性能
- 审查安全配置

## 📞 支持和帮助

### 获取帮助

```bash
# 查看脚本帮助
./deploy.sh help
./health-check.sh help
./scripts/backup.sh help
```

### 有用的命令

```bash
# 查看所有 Black Duck 容器
docker ps --filter name=blackduck

# 查看 Docker 网络
docker network ls
docker network inspect blackduck-deployment_blackduck-net

# 查看数据卷
docker volume ls --filter name=blackduck

# 清理未使用的资源
docker system prune
```

### 联系支持

- **官方文档**: https://documentation.blackduck.com/
- **社区论坛**: https://community.blackduck.com/
- **技术支持**: 联系 Synopsys 技术支持团队

## 📦 离线部署

### 概述

Black Duck 支持完全离线部署，无需网络连接即可在任何支持 Docker 的 Linux 环境中部署演示环境。离线部署包含所有必需的 Docker 镜像文件和自动化脚本。

### 离线部署包内容

```
blackduck-deployment/
├── docker-images/              # Docker 镜像文件目录
│   ├── postgres-15.tar        # PostgreSQL 15 镜像 (417MB)
│   ├── redis-7-alpine.tar     # Redis 7 Alpine 镜像 (40MB)
│   ├── nginx-alpine.tar       # Nginx Alpine 镜像 (51MB)
│   ├── rabbitmq-3-management-alpine.tar  # RabbitMQ 镜像 (172MB)
│   ├── checksums.sha256        # 镜像文件校验和
│   └── images-info.txt         # 镜像信息说明
├── load-images.sh              # 镜像加载脚本
├── docker-compose-offline.yml  # 离线部署配置
├── configs/nginx-offline.conf  # 离线 Nginx 配置
└── demo/offline.html           # 离线部署验证页面
```

### 快速离线部署

#### 1. 准备离线部署包

```bash
# 在有网络的环境中导出镜像
./setup.sh                     # 初始化环境
./deploy.sh                     # 在线部署一次以拉取镜像
./load-images.sh export         # 导出镜像文件

# 将整个 blackduck-deployment 目录复制到目标服务器
```

#### 2. 在目标服务器上部署

```bash
# 加载 Docker 镜像
./load-images.sh

# 离线部署
./deploy.sh offline

# 验证部署
curl http://localhost/offline
```

### 离线部署特性

- ✅ **无网络依赖**: 所有镜像从本地 .tar 文件加载
- ✅ **完整功能**: 包含数据库、缓存、消息队列等所有组件
- ✅ **自动化脚本**: 一键加载和部署
- ✅ **完整性验证**: SHA256 校验确保文件完整性
- ✅ **跨平台支持**: 支持各种 Linux 发行版
- ✅ **演示环境**: 适用于概念验证和功能演示

### 离线部署管理

```bash
# 镜像管理
./load-images.sh                # 加载所有镜像
./load-images.sh verify         # 验证镜像完整性
./load-images.sh list           # 列出已加载镜像
./load-images.sh cleanup        # 清理未使用镜像

# 服务管理
./deploy.sh offline             # 离线部署
./deploy.sh stop offline        # 停止离线服务
./deploy.sh restart offline     # 重启离线服务
./deploy.sh status offline      # 查看离线服务状态
./deploy.sh logs offline        # 查看离线服务日志
```

### 离线部署访问信息

| 服务          | 访问地址                 | 说明                                |
| ------------- | ------------------------ | ----------------------------------- |
| 离线部署页面  | http://localhost/offline | 离线部署验证和状态页面              |
| 主页面        | http://localhost/        | 演示环境主页                        |
| 健康检查      | http://localhost/health  | 返回 "healthy - offline deployment" |
| RabbitMQ 管理 | http://localhost:15672   | 用户名/密码: guest/guest            |

### 系统要求

**最低要求**:

- Docker Engine 20.10+
- 可用磁盘空间: 2GB+ (包含运行时空间)
- 内存: 4GB+ RAM
- CPU: 2+ 核心

**推荐配置**:

- 可用磁盘空间: 5GB+
- 内存: 8GB+ RAM
- CPU: 4+ 核心

### 故障排除

**镜像加载失败**:

```bash
# 验证镜像文件完整性
./load-images.sh verify

# 检查磁盘空间
df -h

# 手动加载单个镜像
docker load -i docker-images/postgres-15.tar
```

**服务启动失败**:

```bash
# 检查镜像是否已加载
./load-images.sh list

# 查看服务日志
./deploy.sh logs offline

# 重启服务
./deploy.sh restart offline
```

**网络访问问题**:

```bash
# 检查端口占用
netstat -tlnp | grep :80

# 检查容器状态
./deploy.sh status offline

# 测试健康检查
curl http://localhost/health
```

## 🎯 生产环境调整建议

### 1. 资源配置优化

**编辑 `.env` 文件，启用生产环境配置**:

```bash
# 取消注释并调整生产环境资源限制
POSTGRES_MEMORY_LIMIT=16G
POSTGRES_CPU_LIMIT=8.0
WEBAPP_MEMORY_LIMIT=16G
WEBAPP_CPU_LIMIT=8.0
SCAN_MEMORY_LIMIT=8G
JOBRUNNER_MEMORY_LIMIT=8G
```

### 2. 高可用配置

**数据库高可用**:

- 配置 PostgreSQL 主从复制
- 使用外部托管数据库服务（如 AWS RDS）
- 实施定期数据库备份策略

**负载均衡**:

- 在 Nginx 前端配置负载均衡器
- 使用多个 webapp 实例
- 配置健康检查和故障转移

### 3. 安全加固

**网络安全**:

- 使用 VPN 或专用网络
- 配置 WAF（Web Application Firewall）
- 限制管理端口访问

**数据加密**:

- 启用数据库加密
- 配置 TLS 1.3
- 使用 HSM 管理密钥

### 4. 监控和告警

**集成监控系统**:

```bash
# 添加 Prometheus 监控
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d
```

**配置告警**:

- 服务不可用告警
- 资源使用率告警
- 安全事件告警

### 5. 备份策略

**自动化备份**:

```bash
# 配置 crontab 定时备份
0 2 * * * /opt/blackduck-deployment/scripts/backup.sh
```

**异地备份**:

- 配置云存储备份
- 实施 3-2-1 备份策略
- 定期测试恢复流程

## 📋 部署检查清单

### 部署前检查

- [ ] 系统资源满足要求
- [ ] Docker 和 Docker Compose 已安装
- [ ] 防火墙规则已配置
- [ ] SSL 证书已准备
- [ ] 环境变量已配置
- [ ] 备份策略已制定

### 部署后验证

- [ ] 所有服务正常启动
- [ ] Web 界面可访问
- [ ] 数据库连接正常
- [ ] API 端点响应正常
- [ ] 健康检查通过
- [ ] 日志记录正常
- [ ] 备份功能测试

### 生产环境额外检查

- [ ] 负载测试完成
- [ ] 安全扫描通过
- [ ] 监控告警配置
- [ ] 文档更新完成
- [ ] 团队培训完成
- [ ] 应急预案制定

---

## 📝 更新日志

- **v1.0** (2025-01-30): 初始版本，支持 Black Duck 2025.4.1
  - 包含完整的 Docker Compose 配置
  - 自动化部署和健康检查脚本
  - 备份和恢复功能
  - 详细的故障排除指南
  - 生产环境部署建议
