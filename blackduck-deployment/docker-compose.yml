version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: blackduck-postgres
    hostname: postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_HOST_AUTH_METHOD=trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./volumes/logs/postgres:/var/log/postgresql
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT:-4G}
          cpus: '${POSTGRES_CPU_LIMIT:-2.0}'
        reservations:
          memory: ${POSTGRES_MEMORY_RESERVATION:-2G}
          cpus: '${POSTGRES_CPU_RESERVATION:-1.0}'

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: blackduck-redis
    hostname: redis
    volumes:
      - redis_data:/data
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT:-1G}
          cpus: '${REDIS_CPU_LIMIT:-0.5}'

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: blackduck-rabbitmq
    hostname: rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: ${RABBITMQ_MEMORY_LIMIT:-1G}
          cpus: '${RABBITMQ_CPU_LIMIT:-0.5}'

  # Authentication Service
  authentication:
    image: blackducksoftware/blackduck-authentication:${BLACKDUCK_VERSION:-2025.4.1}
    container_name: blackduck-authentication
    hostname: authentication
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - HUB_POSTGRES_HOST=postgres
      - HUB_POSTGRES_PORT=5432
      - HUB_POSTGRES_USER=${POSTGRES_USER}
      - HUB_POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - HUB_POSTGRES_ADMIN=${POSTGRES_ADMIN_USER}
      - HUB_POSTGRES_ADMIN_PASSWORD=${POSTGRES_ADMIN_PASSWORD}
    volumes:
      - authentication_data:/opt/blackduck/hub/authentication/security
      - ./volumes/logs/authentication:/opt/blackduck/hub/logs
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8443/api/health-checks/liveness"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: ${AUTH_MEMORY_LIMIT:-2G}
          cpus: '${AUTH_CPU_LIMIT:-1.0}'

  # Web Application
  webapp:
    image: blackducksoftware/blackduck-webapp:${BLACKDUCK_VERSION:-2025.4.1}
    container_name: blackduck-webapp
    hostname: webapp
    depends_on:
      postgres:
        condition: service_healthy
      authentication:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - HUB_POSTGRES_HOST=postgres
      - HUB_POSTGRES_PORT=5432
      - HUB_POSTGRES_USER=${POSTGRES_USER}
      - HUB_POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - HUB_POSTGRES_ADMIN=${POSTGRES_ADMIN_USER}
      - HUB_POSTGRES_ADMIN_PASSWORD=${POSTGRES_ADMIN_PASSWORD}
      - PUBLIC_HUB_WEBSERVER_HOST=${PUBLIC_HUB_HOST}
      - PUBLIC_HUB_WEBSERVER_PORT=${PUBLIC_HUB_PORT}
      - HUB_WEBSERVER_PORT=8443
      - HUB_MAX_MEMORY=${WEBAPP_MAX_MEMORY:-8192m}
    volumes:
      - webapp_data:/opt/blackduck/hub/webapp/security
      - ./volumes/logs/webapp:/opt/blackduck/hub/logs
      - ./volumes/uploads:/opt/blackduck/hub/uploads
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8443/api/health-checks/liveness"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 180s
    deploy:
      resources:
        limits:
          memory: ${WEBAPP_MEMORY_LIMIT:-8G}
          cpus: '${WEBAPP_CPU_LIMIT:-4.0}'
        reservations:
          memory: ${WEBAPP_MEMORY_RESERVATION:-4G}
          cpus: '${WEBAPP_CPU_RESERVATION:-2.0}'

  # Scan Service
  scan:
    image: blackducksoftware/blackduck-scan:${BLACKDUCK_VERSION:-2025.4.1}
    container_name: blackduck-scan
    hostname: scan
    depends_on:
      postgres:
        condition: service_healthy
      authentication:
        condition: service_healthy
    environment:
      - HUB_POSTGRES_HOST=postgres
      - HUB_POSTGRES_PORT=5432
      - HUB_POSTGRES_USER=${POSTGRES_USER}
      - HUB_POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - HUB_MAX_MEMORY=${SCAN_MAX_MEMORY:-4096m}
    volumes:
      - scan_data:/opt/blackduck/hub/scan/security
      - ./volumes/logs/scan:/opt/blackduck/hub/logs
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8443/api/health-checks/liveness"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: ${SCAN_MEMORY_LIMIT:-4G}
          cpus: '${SCAN_CPU_LIMIT:-2.0}'

  # Job Runner
  jobrunner:
    image: blackducksoftware/blackduck-jobrunner:${BLACKDUCK_VERSION:-2025.4.1}
    container_name: blackduck-jobrunner
    hostname: jobrunner
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    environment:
      - HUB_POSTGRES_HOST=postgres
      - HUB_POSTGRES_PORT=5432
      - HUB_POSTGRES_USER=${POSTGRES_USER}
      - HUB_POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - HUB_MAX_MEMORY=${JOBRUNNER_MAX_MEMORY:-4096m}
    volumes:
      - jobrunner_data:/opt/blackduck/hub/jobrunner/security
      - ./volumes/logs/jobrunner:/opt/blackduck/hub/logs
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8443/api/health-checks/liveness"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: ${JOBRUNNER_MEMORY_LIMIT:-4G}
          cpus: '${JOBRUNNER_CPU_LIMIT:-2.0}'

  # BOM Engine
  bomengine:
    image: blackducksoftware/blackduck-bomengine:${BLACKDUCK_VERSION:-2025.4.1}
    container_name: blackduck-bomengine
    hostname: bomengine
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - HUB_POSTGRES_HOST=postgres
      - HUB_POSTGRES_PORT=5432
      - HUB_POSTGRES_USER=${POSTGRES_USER}
      - HUB_POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - HUB_MAX_MEMORY=${BOMENGINE_MAX_MEMORY:-4096m}
    volumes:
      - bomengine_data:/opt/blackduck/hub/bomengine/security
      - ./volumes/logs/bomengine:/opt/blackduck/hub/logs
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8443/api/health-checks/liveness"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: ${BOMENGINE_MEMORY_LIMIT:-4G}
          cpus: '${BOMENGINE_CPU_LIMIT:-2.0}'

  # Match Engine
  matchengine:
    image: blackducksoftware/blackduck-matchengine:${BLACKDUCK_VERSION:-2025.4.1}
    container_name: blackduck-matchengine
    hostname: matchengine
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - HUB_POSTGRES_HOST=postgres
      - HUB_POSTGRES_PORT=5432
      - HUB_POSTGRES_USER=${POSTGRES_USER}
      - HUB_POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - HUB_MAX_MEMORY=${MATCHENGINE_MAX_MEMORY:-4096m}
    volumes:
      - matchengine_data:/opt/blackduck/hub/matchengine/security
      - ./volumes/logs/matchengine:/opt/blackduck/hub/logs
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8443/api/health-checks/liveness"]
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: ${MATCHENGINE_MEMORY_LIMIT:-4G}
          cpus: '${MATCHENGINE_CPU_LIMIT:-2.0}'

  # Nginx Reverse Proxy
  nginx:
    image: blackducksoftware/blackduck-nginx:${BLACKDUCK_VERSION:-2025.4.1}
    container_name: blackduck-nginx
    hostname: nginx
    depends_on:
      webapp:
        condition: service_healthy
    ports:
      - "${HTTP_PORT:-80}:8080"
      - "${HTTPS_PORT:-443}:8443"
    volumes:
      - nginx_data:/opt/blackduck/hub/webserver/security
      - ./configs/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./volumes/logs/nginx:/var/log/nginx
    networks:
      - blackduck-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: ${NGINX_MEMORY_LIMIT:-512M}
          cpus: '${NGINX_CPU_LIMIT:-0.5}'

  # Logstash (Optional)
  logstash:
    image: blackducksoftware/blackduck-logstash:${BLACKDUCK_VERSION:-2025.4.1}
    container_name: blackduck-logstash
    hostname: logstash
    depends_on:
      - webapp
    environment:
      - HUB_POSTGRES_HOST=postgres
      - HUB_POSTGRES_PORT=5432
      - HUB_POSTGRES_USER=${POSTGRES_USER}
      - HUB_POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - logstash_data:/opt/blackduck/hub/logstash/security
      - ./configs/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ./volumes/logs:/opt/blackduck/hub/logs:ro
    networks:
      - blackduck-net
    restart: unless-stopped
    profiles:
      - logging
    deploy:
      resources:
        limits:
          memory: ${LOGSTASH_MEMORY_LIMIT:-2G}
          cpus: '${LOGSTASH_CPU_LIMIT:-1.0}'

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  authentication_data:
    driver: local
  webapp_data:
    driver: local
  scan_data:
    driver: local
  jobrunner_data:
    driver: local
  bomengine_data:
    driver: local
  matchengine_data:
    driver: local
  nginx_data:
    driver: local
  logstash_data:
    driver: local

networks:
  blackduck-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
