# Black Duck 离线部署 Docker 镜像信息
# 生成时间: 2025-07-31

## 镜像列表

1. postgres-15.tar
   - 镜像: postgres:15
   - 大小: 417MB
   - 用途: PostgreSQL 数据库服务
   - SHA256: 8ade8d1d4645588a60aa1f4fddab40312c515e2757e57c1da93473328441c0b1

2. redis-7-alpine.tar
   - 镜像: redis:7-alpine
   - 大小: 40MB
   - 用途: Redis 缓存服务
   - SHA256: 018e306214940c7e26b1d62e0558c13a1fafdaf1dc63ac7617376deef41bdc6b

3. nginx-alpine.tar
   - 镜像: nginx:alpine
   - 大小: 51MB
   - 用途: Nginx 反向代理服务
   - SHA256: ad0ee38d7af26a2d87ebbd88c31f94847bd4b494ebab5036fab98d0b11ab3b13

4. rabbitmq-3-management-alpine.tar
   - 镜像: rabbitmq:3-management-alpine
   - 大小: 172MB
   - 用途: RabbitMQ 消息队列服务
   - SHA256: d4228b90ebd6b9e8dc53ddb5001541d295b1fe12d7bf8ee9be9fe10ff33f44ed

## 总计大小
约 680MB

## 系统要求
- Docker Engine 20.10+
- 可用磁盘空间: 至少 2GB (包含运行时空间)
- 内存: 至少 4GB RAM
- CPU: 至少 2 核心

## 使用说明
1. 将整个 docker-images/ 目录复制到目标服务器
2. 运行 load-images.sh 脚本加载镜像
3. 使用 docker-compose-offline.yml 进行离线部署
