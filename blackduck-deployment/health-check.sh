#!/bin/bash

# Black Duck Health Check Script
# Author: Deployment Automation
# Version: 1.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.yml"
BASE_URL="https://localhost:443"
HTTP_URL="http://localhost:80"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if services are running
check_containers() {
    log "检查容器状态..."
    
    local services=("postgres" "redis" "rabbitmq" "authentication" "webapp" "scan" "jobrunner" "bomengine" "matchengine" "nginx")
    local failed_services=()
    
    for service in "${services[@]}"; do
        local container_name="blackduck-${service}"
        if docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
            local status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "no-healthcheck")
            case "$status" in
                "healthy")
                    success "✓ $service: 运行中且健康"
                    ;;
                "unhealthy")
                    error "✗ $service: 运行中但不健康"
                    failed_services+=("$service")
                    ;;
                "starting")
                    warning "⚠ $service: 正在启动中"
                    ;;
                "no-healthcheck")
                    warning "? $service: 运行中 (无健康检查)"
                    ;;
                *)
                    error "✗ $service: 状态未知 ($status)"
                    failed_services+=("$service")
                    ;;
            esac
        else
            error "✗ $service: 容器未运行"
            failed_services+=("$service")
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        success "所有服务容器状态正常"
        return 0
    else
        error "以下服务存在问题: ${failed_services[*]}"
        return 1
    fi
}

# Check network connectivity
check_network() {
    log "检查网络连接..."
    
    # Check HTTP redirect
    if curl -s -o /dev/null -w "%{http_code}" "$HTTP_URL" | grep -q "30[12]"; then
        success "✓ HTTP 重定向正常"
    else
        warning "⚠ HTTP 重定向可能有问题"
    fi
    
    # Check HTTPS endpoint
    if curl -k -s -o /dev/null -w "%{http_code}" "$BASE_URL" | grep -q "200\|302"; then
        success "✓ HTTPS 端点可访问"
    else
        error "✗ HTTPS 端点不可访问"
        return 1
    fi
    
    # Check API health endpoint
    if curl -k -s -f "$BASE_URL/api/health-checks/liveness" > /dev/null; then
        success "✓ API 健康检查通过"
    else
        warning "⚠ API 健康检查失败"
    fi
}

# Check database connectivity
check_database() {
    log "检查数据库连接..."
    
    local db_container="blackduck-postgres"
    if docker exec "$db_container" pg_isready -U blackduck_user -d bds_hub > /dev/null 2>&1; then
        success "✓ PostgreSQL 数据库连接正常"
    else
        error "✗ PostgreSQL 数据库连接失败"
        return 1
    fi
    
    # Check database size
    local db_size=$(docker exec "$db_container" psql -U blackduck_user -d bds_hub -t -c "SELECT pg_size_pretty(pg_database_size('bds_hub'));" 2>/dev/null | xargs)
    if [ -n "$db_size" ]; then
        log "数据库大小: $db_size"
    fi
}

# Check Redis connectivity
check_redis() {
    log "检查 Redis 连接..."
    
    local redis_container="blackduck-redis"
    if docker exec "$redis_container" redis-cli ping | grep -q "PONG"; then
        success "✓ Redis 连接正常"
    else
        error "✗ Redis 连接失败"
        return 1
    fi
}

# Check RabbitMQ connectivity
check_rabbitmq() {
    log "检查 RabbitMQ 连接..."
    
    local rabbitmq_container="blackduck-rabbitmq"
    if docker exec "$rabbitmq_container" rabbitmq-diagnostics ping > /dev/null 2>&1; then
        success "✓ RabbitMQ 连接正常"
    else
        error "✗ RabbitMQ 连接失败"
        return 1
    fi
}

# Check system resources
check_resources() {
    log "检查系统资源使用情况..."
    
    # Memory usage
    local total_mem=$(free -h | awk '/^Mem:/ {print $2}')
    local used_mem=$(free -h | awk '/^Mem:/ {print $3}')
    local mem_percent=$(free | awk '/^Mem:/ {printf "%.1f", $3/$2 * 100.0}')
    
    log "内存使用: $used_mem / $total_mem (${mem_percent}%)"
    
    if (( $(echo "$mem_percent > 90" | bc -l) )); then
        warning "⚠ 内存使用率过高: ${mem_percent}%"
    elif (( $(echo "$mem_percent > 80" | bc -l) )); then
        warning "⚠ 内存使用率较高: ${mem_percent}%"
    else
        success "✓ 内存使用率正常: ${mem_percent}%"
    fi
    
    # Disk usage
    local disk_usage=$(df -h "$SCRIPT_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    local disk_available=$(df -h "$SCRIPT_DIR" | awk 'NR==2 {print $4}')
    
    log "磁盘使用: ${disk_usage}% (可用: $disk_available)"
    
    if [ "$disk_usage" -gt 90 ]; then
        error "✗ 磁盘使用率过高: ${disk_usage}%"
    elif [ "$disk_usage" -gt 80 ]; then
        warning "⚠ 磁盘使用率较高: ${disk_usage}%"
    else
        success "✓ 磁盘使用率正常: ${disk_usage}%"
    fi
    
    # Docker container resource usage
    log "Docker 容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep blackduck
}

# Check logs for errors
check_logs() {
    log "检查最近的错误日志..."
    
    local services=("webapp" "authentication" "scan" "jobrunner")
    local error_count=0
    
    for service in "${services[@]}"; do
        local container_name="blackduck-${service}"
        local recent_errors=$(docker logs --since=10m "$container_name" 2>&1 | grep -i "error\|exception\|failed" | wc -l)
        
        if [ "$recent_errors" -gt 0 ]; then
            warning "⚠ $service: 发现 $recent_errors 个错误日志条目"
            error_count=$((error_count + recent_errors))
        else
            success "✓ $service: 无错误日志"
        fi
    done
    
    if [ "$error_count" -gt 0 ]; then
        warning "总计发现 $error_count 个错误日志条目"
        warning "使用 './deploy.sh logs [service]' 查看详细日志"
    fi
}

# Generate health report
generate_report() {
    local timestamp=$(date '+%Y-%m-%d_%H-%M-%S')
    local report_file="${SCRIPT_DIR}/health-report-${timestamp}.txt"
    
    log "生成健康检查报告: $report_file"
    
    {
        echo "Black Duck 健康检查报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "容器状态:"
        if command -v docker-compose &> /dev/null; then
            docker-compose -f "$COMPOSE_FILE" ps
        else
            docker compose -f "$COMPOSE_FILE" ps
        fi
        echo ""
        
        echo "系统资源:"
        echo "内存: $(free -h | awk '/^Mem:/ {print $3 "/" $2}')"
        echo "磁盘: $(df -h "$SCRIPT_DIR" | awk 'NR==2 {print $3 "/" $2 " (" $5 " 已使用)"}')"
        echo ""
        
        echo "网络测试:"
        echo "HTTP: $(curl -s -o /dev/null -w "%{http_code}" "$HTTP_URL")"
        echo "HTTPS: $(curl -k -s -o /dev/null -w "%{http_code}" "$BASE_URL")"
        echo ""
        
        echo "服务端点:"
        echo "- Web 界面: $BASE_URL"
        echo "- API 文档: $BASE_URL/api/doc"
        echo ""
        
    } > "$report_file"
    
    success "健康检查报告已保存到: $report_file"
}

# Main health check function
main() {
    echo "========================================"
    echo "Black Duck 健康检查"
    echo "========================================"
    echo ""
    
    local checks_passed=0
    local total_checks=6
    
    if check_containers; then
        ((checks_passed++))
    fi
    
    if check_database; then
        ((checks_passed++))
    fi
    
    if check_redis; then
        ((checks_passed++))
    fi
    
    if check_rabbitmq; then
        ((checks_passed++))
    fi
    
    if check_network; then
        ((checks_passed++))
    fi
    
    check_resources
    ((checks_passed++))
    
    check_logs
    
    echo ""
    echo "========================================"
    echo "健康检查总结"
    echo "========================================"
    
    if [ "$checks_passed" -eq "$total_checks" ]; then
        success "所有检查通过 ($checks_passed/$total_checks)"
        echo ""
        echo "Black Duck 运行状态良好！"
        echo "访问地址: $BASE_URL"
    else
        error "部分检查失败 ($checks_passed/$total_checks)"
        echo ""
        echo "请检查上述错误并采取相应措施"
    fi
    
    # Generate report if requested
    if [ "${1:-}" = "--report" ]; then
        generate_report
    fi
}

# Handle script arguments
case "${1:-}" in
    ""|"--report")
        main "$1"
        ;;
    "containers")
        check_containers
        ;;
    "network")
        check_network
        ;;
    "database")
        check_database
        ;;
    "redis")
        check_redis
        ;;
    "rabbitmq")
        check_rabbitmq
        ;;
    "resources")
        check_resources
        ;;
    "logs")
        check_logs
        ;;
    "help"|"-h"|"--help")
        echo "Black Duck 健康检查脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  (无)             执行完整健康检查"
        echo "  --report         执行检查并生成报告"
        echo "  containers       仅检查容器状态"
        echo "  network          仅检查网络连接"
        echo "  database         仅检查数据库"
        echo "  redis            仅检查 Redis"
        echo "  rabbitmq         仅检查 RabbitMQ"
        echo "  resources        仅检查系统资源"
        echo "  logs             仅检查错误日志"
        echo "  help             显示此帮助信息"
        echo ""
        ;;
    *)
        error "未知选项: $1"
        echo "使用 '$0 help' 查看可用选项"
        exit 1
        ;;
esac
