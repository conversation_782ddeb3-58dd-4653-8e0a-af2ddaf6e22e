# Black Duck 快速部署指南

## 🚀 5分钟快速部署

### 1. 系统检查
```bash
# 检查系统资源
free -h  # 至少需要 16GB 内存
df -h    # 至少需要 100GB 可用空间

# 检查 Docker
docker --version  # 需要 20.10+
docker-compose --version  # 需要 1.29+
```

### 2. 下载部署文件
```bash
# 进入部署目录
cd /opt
git clone <repository-url> blackduck-deployment
cd blackduck-deployment

# 设置执行权限
chmod +x *.sh scripts/*.sh
```

### 3. 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置（必须修改密码）
vim .env

# 重点修改以下配置：
# POSTGRES_PASSWORD=your_secure_password_here
# POSTGRES_ADMIN_PASSWORD=your_admin_password_here  
# RABBITMQ_PASSWORD=your_rabbitmq_password_here
# PUBLIC_HUB_HOST=localhost  # 或您的域名
```

### 4. 一键部署
```bash
# 执行自动化部署
./deploy.sh

# 等待部署完成（约10-15分钟）
```

### 5. 验证部署
```bash
# 运行健康检查
./health-check.sh

# 访问 Web 界面
# https://localhost:443
# 用户名: sysadmin
# 密码: blackduck
```

## 🔧 常用管理命令

```bash
# 查看服务状态
./deploy.sh status

# 查看日志
./deploy.sh logs webapp

# 重启服务
./deploy.sh restart

# 停止服务
./deploy.sh stop

# 备份数据
./scripts/backup.sh

# 恢复数据
./scripts/restore.sh
```

## 🚨 快速故障排除

### 服务启动失败
```bash
# 检查资源使用
docker stats
free -h

# 查看错误日志
./deploy.sh logs postgres
./deploy.sh logs webapp
```

### Web界面无法访问
```bash
# 检查端口占用
netstat -tlnp | grep :443

# 检查防火墙
sudo ufw status
sudo firewall-cmd --list-all

# 重启 nginx
docker-compose restart nginx
```

### 数据库连接失败
```bash
# 检查数据库状态
docker exec blackduck-postgres pg_isready -U blackduck_user -d bds_hub

# 重启数据库
docker-compose restart postgres
```

## 📋 部署检查清单

**部署前：**
- [ ] 系统资源充足（16GB+ RAM, 100GB+ 存储）
- [ ] Docker 和 Docker Compose 已安装
- [ ] 端口 80 和 443 可用
- [ ] 环境变量已配置
- [ ] 密码已修改

**部署后：**
- [ ] 所有容器正常运行
- [ ] Web 界面可访问 (https://localhost:443)
- [ ] 健康检查通过
- [ ] 默认密码已修改
- [ ] 备份策略已配置

## 🎯 生产环境快速配置

### 1. 调整资源配置
```bash
# 编辑 .env 文件，启用生产环境配置
POSTGRES_MEMORY_LIMIT=16G
WEBAPP_MEMORY_LIMIT=16G
SCAN_MEMORY_LIMIT=8G
```

### 2. 配置 SSL 证书
```bash
# 将证书文件放置到 volumes/certs/ 目录
mkdir -p volumes/certs
cp your-cert.crt volumes/certs/
cp your-key.key volumes/certs/
```

### 3. 配置自动备份
```bash
# 添加到 crontab
crontab -e

# 每天凌晨2点自动备份
0 2 * * * /opt/blackduck-deployment/scripts/backup.sh
```

### 4. 配置监控
```bash
# 启用 Logstash（可选）
docker-compose --profile logging up -d

# 配置外部监控系统
# - Prometheus + Grafana
# - ELK Stack
# - 自定义监控脚本
```

## 📞 获取帮助

```bash
# 查看详细帮助
./deploy.sh help
./health-check.sh help
./scripts/backup.sh help
./scripts/restore.sh help

# 查看完整文档
cat README.md
```

## 🔗 有用链接

- **官方文档**: https://documentation.blackduck.com/
- **社区论坛**: https://community.blackduck.com/
- **Docker Hub**: https://hub.docker.com/u/blackducksoftware
- **GitHub 仓库**: https://github.com/blackducksoftware/hub

---

**注意**: 首次部署可能需要10-15分钟完全初始化，请耐心等待。如遇问题，请查看详细的 README.md 文档或运行健康检查脚本进行诊断。
