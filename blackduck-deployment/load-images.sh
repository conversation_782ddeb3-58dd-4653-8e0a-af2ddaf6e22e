#!/bin/bash

# Black Duck 离线部署 - Docker 镜像加载脚本
# Author: Deployment Automation
# Version: 1.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
IMAGES_DIR="${SCRIPT_DIR}/docker-images"
CHECKSUMS_FILE="${IMAGES_DIR}/checksums.sha256"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Print banner
print_banner() {
    echo ""
    echo "========================================"
    echo "  Black Duck 离线部署 - 镜像加载器"
    echo "========================================"
    echo ""
}

# Check prerequisites
check_prerequisites() {
    log "检查系统先决条件..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker 未安装。请先安装 Docker。"
        exit 1
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error "Docker 守护进程未运行。请启动 Docker 服务。"
        exit 1
    fi
    
    # Check images directory
    if [ ! -d "$IMAGES_DIR" ]; then
        error "镜像目录不存在: $IMAGES_DIR"
        exit 1
    fi
    
    success "系统先决条件检查通过"
}

# Verify checksums
verify_checksums() {
    log "验证镜像文件完整性..."
    
    if [ ! -f "$CHECKSUMS_FILE" ]; then
        warning "校验文件不存在，跳过完整性验证"
        return 0
    fi
    
    cd "$IMAGES_DIR"
    
    # Use appropriate checksum command
    if command -v sha256sum &> /dev/null; then
        if sha256sum -c checksums.sha256 --quiet; then
            success "镜像文件完整性验证通过"
        else
            error "镜像文件完整性验证失败"
            return 1
        fi
    elif command -v shasum &> /dev/null; then
        if shasum -a 256 -c checksums.sha256 --quiet; then
            success "镜像文件完整性验证通过"
        else
            error "镜像文件完整性验证失败"
            return 1
        fi
    else
        warning "无法找到校验工具，跳过完整性验证"
    fi
    
    cd "$SCRIPT_DIR"
}

# Load Docker images
load_images() {
    log "开始加载 Docker 镜像..."
    
    local images=(
        "postgres-15.tar:postgres:15"
        "redis-7-alpine.tar:redis:7-alpine"
        "nginx-alpine.tar:nginx:alpine"
        "rabbitmq-3-management-alpine.tar:rabbitmq:3-management-alpine"
    )
    
    local loaded_count=0
    local total_count=${#images[@]}
    
    for image_info in "${images[@]}"; do
        local tar_file=$(echo "$image_info" | cut -d':' -f1)
        local image_name=$(echo "$image_info" | cut -d':' -f2-)
        local tar_path="${IMAGES_DIR}/${tar_file}"
        
        if [ ! -f "$tar_path" ]; then
            error "镜像文件不存在: $tar_path"
            continue
        fi
        
        log "加载镜像: $image_name"
        
        if docker load -i "$tar_path"; then
            success "✓ $image_name 加载成功"
            ((loaded_count++))
        else
            error "✗ $image_name 加载失败"
        fi
    done
    
    echo ""
    log "镜像加载完成: $loaded_count/$total_count"
    
    if [ $loaded_count -eq $total_count ]; then
        success "所有镜像加载成功！"
        return 0
    else
        error "部分镜像加载失败"
        return 1
    fi
}

# List loaded images
list_loaded_images() {
    log "已加载的镜像列表:"
    echo ""
    
    local images=("postgres:15" "redis:7-alpine" "nginx:alpine" "rabbitmq:3-management-alpine")
    
    for image in "${images[@]}"; do
        if docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep -q "^$image"; then
            echo "✓ $image"
        else
            echo "✗ $image (未找到)"
        fi
    done
    
    echo ""
    log "详细镜像信息:"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep -E "(postgres|redis|nginx|rabbitmq|REPOSITORY)"
}

# Clean up old images (optional)
cleanup_old_images() {
    log "清理旧镜像..."
    
    read -p "是否清理未使用的镜像? (y/N): " cleanup_confirm
    
    if [[ "$cleanup_confirm" =~ ^[Yy]$ ]]; then
        docker image prune -f
        success "旧镜像清理完成"
    else
        log "跳过镜像清理"
    fi
}

# Show disk usage
show_disk_usage() {
    log "Docker 磁盘使用情况:"
    docker system df
    
    echo ""
    log "镜像文件磁盘使用:"
    du -sh "$IMAGES_DIR"
}

# Main function
main() {
    print_banner
    
    check_prerequisites
    verify_checksums
    load_images
    list_loaded_images
    show_disk_usage
    
    echo ""
    success "Docker 镜像加载完成！"
    echo ""
    echo "下一步操作:"
    echo "1. 运行 './deploy.sh offline' 开始离线部署"
    echo "2. 或使用 'docker compose -f docker-compose-offline.yml up -d'"
    echo ""
}

# Handle script arguments
case "${1:-}" in
    ""|"load")
        main
        ;;
    "verify")
        check_prerequisites
        verify_checksums
        ;;
    "list")
        list_loaded_images
        ;;
    "cleanup")
        cleanup_old_images
        ;;
    "help"|"-h"|"--help")
        echo "Black Duck 离线部署 - 镜像加载脚本"
        echo ""
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  (无), load       加载所有镜像 (默认)"
        echo "  verify           仅验证镜像完整性"
        echo "  list             列出已加载的镜像"
        echo "  cleanup          清理未使用的镜像"
        echo "  help             显示此帮助信息"
        echo ""
        ;;
    *)
        error "未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac
