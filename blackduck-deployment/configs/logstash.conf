# Black Duck Logstash Configuration
# Processes and forwards Black Duck application logs

input {
  # File input for webapp logs
  file {
    path => "/opt/blackduck/hub/logs/webapp/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => multiline {
      pattern => "^%{TIMESTAMP_ISO8601}"
      negate => true
      what => "previous"
    }
    tags => ["webapp"]
  }

  # File input for authentication logs
  file {
    path => "/opt/blackduck/hub/logs/authentication/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => multiline {
      pattern => "^%{TIMESTAMP_ISO8601}"
      negate => true
      what => "previous"
    }
    tags => ["authentication"]
  }

  # File input for scan logs
  file {
    path => "/opt/blackduck/hub/logs/scan/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => multiline {
      pattern => "^%{TIMESTAMP_ISO8601}"
      negate => true
      what => "previous"
    }
    tags => ["scan"]
  }

  # File input for jobrunner logs
  file {
    path => "/opt/blackduck/hub/logs/jobrunner/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => multiline {
      pattern => "^%{TIMESTAMP_ISO8601}"
      negate => true
      what => "previous"
    }
    tags => ["jobrunner"]
  }

  # File input for bomengine logs
  file {
    path => "/opt/blackduck/hub/logs/bomengine/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => multiline {
      pattern => "^%{TIMESTAMP_ISO8601}"
      negate => true
      what => "previous"
    }
    tags => ["bomengine"]
  }

  # File input for matchengine logs
  file {
    path => "/opt/blackduck/hub/logs/matchengine/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => multiline {
      pattern => "^%{TIMESTAMP_ISO8601}"
      negate => true
      what => "previous"
    }
    tags => ["matchengine"]
  }
}

filter {
  # Parse timestamp
  if [message] =~ /^\d{4}-\d{2}-\d{2}/ {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:log_message}" 
      }
    }
    
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS" ]
    }
  }

  # Add service field based on tags
  if "webapp" in [tags] {
    mutate { add_field => { "service" => "webapp" } }
  } else if "authentication" in [tags] {
    mutate { add_field => { "service" => "authentication" } }
  } else if "scan" in [tags] {
    mutate { add_field => { "service" => "scan" } }
  } else if "jobrunner" in [tags] {
    mutate { add_field => { "service" => "jobrunner" } }
  } else if "bomengine" in [tags] {
    mutate { add_field => { "service" => "bomengine" } }
  } else if "matchengine" in [tags] {
    mutate { add_field => { "service" => "matchengine" } }
  }

  # Parse Java stack traces
  if [level] == "ERROR" and [log_message] =~ /Exception|Error/ {
    mutate { add_tag => ["exception"] }
  }

  # Parse performance metrics
  if [log_message] =~ /took \d+ms/ {
    grok {
      match => { 
        "log_message" => "took %{NUMBER:duration_ms:int}ms" 
      }
    }
    mutate { add_tag => ["performance"] }
  }

  # Parse database queries
  if [log_message] =~ /SQL|Query/ {
    mutate { add_tag => ["database"] }
  }

  # Parse security events
  if [log_message] =~ /authentication|login|logout|unauthorized|forbidden/ {
    mutate { add_tag => ["security"] }
  }

  # Clean up fields
  mutate {
    remove_field => [ "host", "path" ]
  }
}

output {
  # Output to stdout for debugging (remove in production)
  stdout {
    codec => rubydebug
  }

  # Output to Elasticsearch (uncomment if using ELK stack)
  # elasticsearch {
  #   hosts => ["elasticsearch:9200"]
  #   index => "blackduck-logs-%{+YYYY.MM.dd}"
  # }

  # Output to file for archival
  file {
    path => "/opt/blackduck/hub/logs/processed/blackduck-%{service}-%{+YYYY.MM.dd}.log"
    codec => json_lines
  }

  # Output errors to separate file
  if [level] == "ERROR" {
    file {
      path => "/opt/blackduck/hub/logs/errors/blackduck-errors-%{+YYYY.MM.dd}.log"
      codec => json_lines
    }
  }

  # Output performance metrics
  if "performance" in [tags] {
    file {
      path => "/opt/blackduck/hub/logs/metrics/blackduck-performance-%{+YYYY.MM.dd}.log"
      codec => json_lines
    }
  }

  # Output security events
  if "security" in [tags] {
    file {
      path => "/opt/blackduck/hub/logs/security/blackduck-security-%{+YYYY.MM.dd}.log"
      codec => json_lines
    }
  }
}
