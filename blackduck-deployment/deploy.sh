#!/bin/bash

# Black Duck Docker Compose Deployment Script
# Author: Deployment Automation
# Version: 1.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_FILE="${SCRIPT_DIR}/.env"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.yml"
LOG_FILE="${SCRIPT_DIR}/deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "检查系统先决条件..."

    # Check Docker
    if ! command -v docker &>/dev/null; then
        error "Docker 未安装。请先安装 Docker。"
        exit 1
    fi

    # Check Docker Compose
    if ! command -v docker-compose &>/dev/null && ! docker compose version &>/dev/null; then
        error "Docker Compose 未安装。请先安装 Docker Compose。"
        exit 1
    fi

    # Check Docker daemon
    if ! docker info &>/dev/null; then
        error "Docker 守护进程未运行。请启动 Docker 服务。"
        exit 1
    fi

    # Check system resources
    TOTAL_MEM=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$TOTAL_MEM" -lt 16 ]; then
        warning "系统内存少于16GB，可能影响性能。建议至少16GB内存。"
    fi

    # Check disk space
    AVAILABLE_SPACE=$(df -BG "$SCRIPT_DIR" | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$AVAILABLE_SPACE" -lt 50 ]; then
        warning "可用磁盘空间少于50GB，可能不足以运行Black Duck。"
    fi

    success "系统先决条件检查完成"
}

# Create necessary directories
create_directories() {
    log "创建必要的目录结构..."

    mkdir -p "${SCRIPT_DIR}/volumes/logs/postgres"
    mkdir -p "${SCRIPT_DIR}/volumes/logs/webapp"
    mkdir -p "${SCRIPT_DIR}/volumes/logs/authentication"
    mkdir -p "${SCRIPT_DIR}/volumes/logs/scan"
    mkdir -p "${SCRIPT_DIR}/volumes/logs/jobrunner"
    mkdir -p "${SCRIPT_DIR}/volumes/logs/bomengine"
    mkdir -p "${SCRIPT_DIR}/volumes/logs/matchengine"
    mkdir -p "${SCRIPT_DIR}/volumes/logs/nginx"
    mkdir -p "${SCRIPT_DIR}/volumes/uploads"
    mkdir -p "${SCRIPT_DIR}/configs"
    mkdir -p "${SCRIPT_DIR}/scripts"

    # Set proper permissions
    chmod -R 755 "${SCRIPT_DIR}/volumes"

    success "目录结构创建完成"
}

# Check environment file
check_environment() {
    log "检查环境配置..."

    if [ ! -f "$ENV_FILE" ]; then
        warning ".env 文件不存在，从模板创建..."
        if [ -f "${SCRIPT_DIR}/.env.example" ]; then
            cp "${SCRIPT_DIR}/.env.example" "$ENV_FILE"
            warning "请编辑 .env 文件并设置适当的密码和配置"
            warning "特别注意修改以下配置项："
            warning "- POSTGRES_PASSWORD"
            warning "- POSTGRES_ADMIN_PASSWORD"
            warning "- RABBITMQ_PASSWORD"
            warning "- PUBLIC_HUB_HOST"
            read -p "按回车键继续，或按 Ctrl+C 退出编辑配置文件..."
        else
            error ".env.example 文件不存在，无法创建环境配置"
            exit 1
        fi
    fi

    # Check for default passwords
    if grep -q "your_secure_password_here\|your_admin_password_here\|your_rabbitmq_password_here" "$ENV_FILE"; then
        warning "检测到默认密码，建议修改为安全密码"
    fi

    success "环境配置检查完成"
}

# Pull Docker images
pull_images() {
    log "拉取 Docker 镜像..."

    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$COMPOSE_FILE" pull
    else
        docker compose -f "$COMPOSE_FILE" pull
    fi

    success "Docker 镜像拉取完成"
}

# Deploy services
deploy_services() {
    log "部署 Black Duck 服务..."

    # Start core services first
    log "启动核心服务 (PostgreSQL, Redis, RabbitMQ)..."
    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$COMPOSE_FILE" up -d postgres redis rabbitmq
    else
        docker compose -f "$COMPOSE_FILE" up -d postgres redis rabbitmq
    fi

    # Wait for core services
    log "等待核心服务启动..."
    sleep 30

    # Start application services
    log "启动应用服务..."
    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$COMPOSE_FILE" up -d
    else
        docker compose -f "$COMPOSE_FILE" up -d
    fi

    success "Black Duck 服务部署完成"
}

# Wait for services to be ready
wait_for_services() {
    log "等待服务就绪..."

    local max_attempts=60
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log "检查服务状态 (尝试 $attempt/$max_attempts)..."

        # Check if webapp is responding
        if curl -k -f -s https://localhost:443/api/health-checks/liveness >/dev/null 2>&1; then
            success "Black Duck Web 应用已就绪"
            break
        fi

        if [ $attempt -eq $max_attempts ]; then
            error "服务启动超时，请检查日志"
            return 1
        fi

        sleep 30
        ((attempt++))
    done
}

# Show deployment status
show_status() {
    log "显示部署状态..."

    echo ""
    echo "=================================="
    echo "Black Duck 部署状态"
    echo "=================================="

    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$COMPOSE_FILE" ps
    else
        docker compose -f "$COMPOSE_FILE" ps
    fi

    echo ""
    echo "访问信息："
    echo "- Web 界面: https://localhost:443"
    echo "- HTTP 重定向: http://localhost:80"
    echo ""
    echo "默认登录信息："
    echo "- 用户名: sysadmin"
    echo "- 密码: blackduck"
    echo ""
    echo "重要提示："
    echo "1. 首次启动可能需要10-15分钟完全初始化"
    echo "2. 请及时修改默认密码"
    echo "3. 建议配置SSL证书用于生产环境"
    echo ""
}

# Main deployment function
main() {
    log "开始 Black Duck 部署..."

    check_prerequisites
    create_directories
    check_environment
    pull_images
    deploy_services
    wait_for_services
    show_status

    success "Black Duck 部署完成！"
}

# Offline deployment function
deploy_offline() {
    log "开始离线部署 Black Duck..."

    local offline_compose_file="${SCRIPT_DIR}/docker-compose-offline.yml"

    if [ ! -f "$offline_compose_file" ]; then
        error "离线部署配置文件不存在: $offline_compose_file"
        exit 1
    fi

    # Check if images are loaded
    log "检查离线镜像..."
    local required_images=("postgres:15" "redis:7-alpine" "nginx:alpine" "rabbitmq:3-management-alpine")
    local missing_images=()

    for image in "${required_images[@]}"; do
        if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$image$"; then
            missing_images+=("$image")
        fi
    done

    if [ ${#missing_images[@]} -gt 0 ]; then
        error "以下镜像未找到: ${missing_images[*]}"
        warning "请先运行 './load-images.sh' 加载离线镜像"
        exit 1
    fi

    success "所有离线镜像已准备就绪"

    check_prerequisites
    create_directories
    check_environment

    log "使用离线配置部署服务..."
    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$offline_compose_file" up -d
    else
        docker compose -f "$offline_compose_file" up -d
    fi

    wait_for_services

    echo ""
    success "离线部署完成！"
    echo ""
    echo "访问信息："
    echo "- 离线部署页面: http://localhost/offline"
    echo "- 主页面: http://localhost/"
    echo "- 健康检查: http://localhost/health"
    echo ""
}

# Handle script arguments
case "${1:-}" in
"start" | "deploy" | "")
    main
    ;;
"offline")
    deploy_offline
    ;;
"stop")
    log "停止 Black Duck 服务..."
    compose_file="${COMPOSE_FILE}"
    if [ "$2" = "offline" ]; then
        compose_file="${SCRIPT_DIR}/docker-compose-offline.yml"
    fi

    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$compose_file" down
    else
        docker compose -f "$compose_file" down
    fi
    success "Black Duck 服务已停止"
    ;;
"restart")
    log "重启 Black Duck 服务..."
    compose_file="${COMPOSE_FILE}"
    if [ "$2" = "offline" ]; then
        compose_file="${SCRIPT_DIR}/docker-compose-offline.yml"
    fi

    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$compose_file" restart
    else
        docker compose -f "$compose_file" restart
    fi
    success "Black Duck 服务已重启"
    ;;
"status")
    compose_file="${COMPOSE_FILE}"
    if [ "$2" = "offline" ]; then
        compose_file="${SCRIPT_DIR}/docker-compose-offline.yml"
    fi

    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$compose_file" ps
    else
        docker compose -f "$compose_file" ps
    fi
    ;;
"logs")
    compose_file="${COMPOSE_FILE}"
    if [ "$2" = "offline" ]; then
        compose_file="${SCRIPT_DIR}/docker-compose-offline.yml"
        shift
    fi

    if command -v docker-compose &>/dev/null; then
        docker-compose -f "$compose_file" logs -f "${2:-}"
    else
        docker compose -f "$compose_file" logs -f "${2:-}"
    fi
    ;;
"help" | "-h" | "--help")
    echo "Black Duck 部署脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start, deploy    部署 Black Duck (默认)"
    echo "  offline          离线部署 (需要先运行 load-images.sh)"
    echo "  stop [offline]   停止所有服务"
    echo "  restart [offline] 重启所有服务"
    echo "  status [offline] 显示服务状态"
    echo "  logs [offline] [service] 显示日志"
    echo "  help             显示此帮助信息"
    echo ""
    echo "离线部署示例:"
    echo "  $0 offline       # 离线部署"
    echo "  $0 stop offline  # 停止离线部署"
    echo "  $0 logs offline webapp # 查看离线部署的webapp日志"
    echo ""
    ;;
*)
    error "未知命令: $1"
    echo "使用 '$0 help' 查看可用命令"
    exit 1
    ;;
esac
