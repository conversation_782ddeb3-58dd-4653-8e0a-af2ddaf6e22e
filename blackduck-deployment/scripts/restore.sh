#!/bin/bash

# Black Duck Restore Script
# Author: Deployment Automation
# Version: 1.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${PROJECT_DIR}/backups"

# Database configuration
DB_CONTAINER="blackduck-postgres"
DB_NAME="bds_hub"
DB_USER="blackduck_user"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# List available backups
list_backups() {
    log "可用的备份文件:"
    
    if [ -d "$BACKUP_DIR" ] && [ "$(ls -A "$BACKUP_DIR"/blackduck_backup_*.tar.gz 2>/dev/null)" ]; then
        echo ""
        printf "%-30s %-15s %-20s\n" "备份文件" "大小" "创建时间"
        echo "=================================================================="
        
        for backup in "$BACKUP_DIR"/blackduck_backup_*.tar.gz; do
            if [ -f "$backup" ]; then
                local filename=$(basename "$backup")
                local size=$(du -sh "$backup" | cut -f1)
                local date=$(stat -c %y "$backup" | cut -d' ' -f1,2 | cut -d'.' -f1)
                printf "%-30s %-15s %-20s\n" "$filename" "$size" "$date"
            fi
        done
        echo ""
    else
        warning "未找到备份文件"
        return 1
    fi
}

# Select backup file
select_backup() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        list_backups
        echo ""
        read -p "请输入要恢复的备份文件名: " backup_file
    fi
    
    # Add .tar.gz extension if not present
    if [[ ! "$backup_file" =~ \.tar\.gz$ ]]; then
        backup_file="${backup_file}.tar.gz"
    fi
    
    # Add full path if not present
    if [[ ! "$backup_file" =~ ^/ ]]; then
        backup_file="$BACKUP_DIR/$backup_file"
    fi
    
    if [ ! -f "$backup_file" ]; then
        error "备份文件不存在: $backup_file"
        return 1
    fi
    
    echo "$backup_file"
}

# Verify backup file
verify_backup() {
    local backup_file="$1"
    
    log "验证备份文件: $(basename "$backup_file")"
    
    # Test archive integrity
    if ! tar tzf "$backup_file" > /dev/null 2>&1; then
        error "备份文件损坏或无法读取"
        return 1
    fi
    
    # Check required components
    local required_files=("backup_info.txt")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if ! tar tzf "$backup_file" | grep -q "$file"; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        warning "备份文件中缺少以下组件: ${missing_files[*]}"
    fi
    
    success "备份文件验证通过"
}

# Extract backup
extract_backup() {
    local backup_file="$1"
    local extract_dir="$2"
    
    log "解压备份文件到: $extract_dir"
    
    mkdir -p "$extract_dir"
    
    if tar xzf "$backup_file" -C "$extract_dir" --strip-components=1; then
        success "备份文件解压完成"
    else
        error "备份文件解压失败"
        return 1
    fi
}

# Stop services
stop_services() {
    log "停止 Black Duck 服务..."
    
    cd "$PROJECT_DIR"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose down
    else
        docker compose down
    fi
    
    success "服务已停止"
}

# Restore database
restore_database() {
    local restore_dir="$1"
    local db_backup_file="$restore_dir/database.sql.gz"
    
    if [ ! -f "$db_backup_file" ]; then
        warning "数据库备份文件不存在，跳过数据库恢复"
        return 0
    fi
    
    log "恢复 PostgreSQL 数据库..."
    
    # Start only postgres service
    cd "$PROJECT_DIR"
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d postgres
    else
        docker compose up -d postgres
    fi
    
    # Wait for postgres to be ready
    log "等待 PostgreSQL 启动..."
    local attempts=0
    local max_attempts=30
    
    while [ $attempts -lt $max_attempts ]; do
        if docker exec "$DB_CONTAINER" pg_isready -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
            break
        fi
        sleep 2
        ((attempts++))
    done
    
    if [ $attempts -eq $max_attempts ]; then
        error "PostgreSQL 启动超时"
        return 1
    fi
    
    # Drop existing database and recreate
    log "重建数据库..."
    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -c "DROP DATABASE IF EXISTS $DB_NAME;"
    docker exec "$DB_CONTAINER" psql -U "$DB_USER" -c "CREATE DATABASE $DB_NAME;"
    
    # Restore database
    log "导入数据库备份..."
    gunzip -c "$db_backup_file" | docker exec -i "$DB_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME"
    
    if [ $? -eq 0 ]; then
        success "数据库恢复完成"
    else
        error "数据库恢复失败"
        return 1
    fi
}

# Restore Docker volumes
restore_volumes() {
    local restore_dir="$1"
    local volumes_dir="$restore_dir/volumes"
    
    if [ ! -d "$volumes_dir" ]; then
        warning "数据卷备份目录不存在，跳过数据卷恢复"
        return 0
    fi
    
    log "恢复 Docker 数据卷..."
    
    # Remove existing volumes
    log "清理现有数据卷..."
    local volumes=$(docker volume ls --filter name=blackduck --format "{{.Name}}" 2>/dev/null || true)
    
    for volume in $volumes; do
        log "删除数据卷: $volume"
        docker volume rm "$volume" 2>/dev/null || true
    done
    
    # Restore volumes from backup
    for volume_backup in "$volumes_dir"/*.tar.gz; do
        if [ -f "$volume_backup" ]; then
            local volume_name=$(basename "$volume_backup" .tar.gz)
            log "恢复数据卷: $volume_name"
            
            # Create volume
            docker volume create "$volume_name"
            
            # Restore volume data
            docker run --rm \
                -v "$volume_name:/data" \
                -v "$volumes_dir:/backup:ro" \
                alpine:latest \
                tar xzf "/backup/$(basename "$volume_backup")" -C /data
            
            if [ $? -eq 0 ]; then
                success "数据卷 $volume_name 恢复完成"
            else
                error "数据卷 $volume_name 恢复失败"
            fi
        fi
    done
}

# Restore configuration files
restore_configs() {
    local restore_dir="$1"
    local config_backup_dir="$restore_dir/configs"
    
    if [ ! -d "$config_backup_dir" ]; then
        warning "配置文件备份目录不存在，跳过配置文件恢复"
        return 0
    fi
    
    log "恢复配置文件..."
    
    # Backup current configs
    if [ -d "$PROJECT_DIR/configs" ]; then
        log "备份当前配置文件..."
        mv "$PROJECT_DIR/configs" "$PROJECT_DIR/configs.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Restore configs
    if [ -d "$config_backup_dir/configs" ]; then
        cp -r "$config_backup_dir/configs" "$PROJECT_DIR/"
        success "配置文件恢复完成"
    fi
    
    # Restore environment file
    if [ -f "$config_backup_dir/.env" ]; then
        log "恢复环境配置文件..."
        cp "$config_backup_dir/.env" "$PROJECT_DIR/"
        success "环境配置文件恢复完成"
    fi
    
    # Restore docker-compose file
    if [ -f "$config_backup_dir/docker-compose.yml" ]; then
        log "恢复 Docker Compose 配置文件..."
        cp "$config_backup_dir/docker-compose.yml" "$PROJECT_DIR/"
        success "Docker Compose 配置文件恢复完成"
    fi
}

# Restore logs
restore_logs() {
    local restore_dir="$1"
    local logs_backup="$restore_dir/logs.tar.gz"
    
    if [ ! -f "$logs_backup" ]; then
        warning "日志备份文件不存在，跳过日志恢复"
        return 0
    fi
    
    log "恢复日志文件..."
    
    # Create logs directory
    mkdir -p "$PROJECT_DIR/volumes/logs"
    
    # Extract logs
    tar xzf "$logs_backup" -C "$PROJECT_DIR/volumes/logs/"
    
    success "日志文件恢复完成"
}

# Start services
start_services() {
    log "启动 Black Duck 服务..."
    
    cd "$PROJECT_DIR"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi
    
    success "服务启动完成"
}

# Verify restoration
verify_restoration() {
    log "验证恢复结果..."
    
    # Wait for services to start
    sleep 30
    
    # Run health check
    if [ -f "$PROJECT_DIR/health-check.sh" ]; then
        "$PROJECT_DIR/health-check.sh"
    else
        warning "健康检查脚本不存在，请手动验证服务状态"
    fi
}

# Show restoration info
show_restoration_info() {
    local restore_dir="$1"
    local info_file="$restore_dir/backup_info.txt"
    
    if [ -f "$info_file" ]; then
        log "备份信息:"
        echo ""
        cat "$info_file"
        echo ""
    fi
    
    echo "=================================="
    echo "恢复完成信息"
    echo "=================================="
    echo "- 恢复时间: $(date)"
    echo "- 访问地址: https://localhost:443"
    echo "- 默认用户: sysadmin"
    echo "- 默认密码: blackduck"
    echo ""
    echo "重要提示:"
    echo "1. 请验证所有服务正常运行"
    echo "2. 检查数据完整性"
    echo "3. 更新密码和安全配置"
    echo ""
}

# Main restore function
main() {
    local backup_file="$1"
    local force_restore="$2"
    
    log "开始 Black Duck 数据恢复..."
    
    # Select backup file
    backup_file=$(select_backup "$backup_file")
    if [ $? -ne 0 ]; then
        exit 1
    fi
    
    # Verify backup
    verify_backup "$backup_file"
    
    # Confirm restoration
    if [ "$force_restore" != "--force" ]; then
        echo ""
        warning "此操作将完全替换当前的 Black Duck 数据和配置"
        warning "建议在恢复前创建当前数据的备份"
        echo ""
        read -p "确认继续恢复? (y/N): " confirm
        
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            log "恢复操作已取消"
            exit 0
        fi
    fi
    
    # Create temporary restore directory
    local restore_dir="/tmp/blackduck_restore_$(date +%Y%m%d_%H%M%S)"
    
    local start_time=$(date +%s)
    
    # Perform restoration steps
    extract_backup "$backup_file" "$restore_dir"
    stop_services
    restore_database "$restore_dir"
    restore_volumes "$restore_dir"
    restore_configs "$restore_dir"
    restore_logs "$restore_dir"
    start_services
    verify_restoration
    show_restoration_info "$restore_dir"
    
    # Cleanup
    rm -rf "$restore_dir"
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    success "Black Duck 数据恢复完成！"
    log "恢复耗时: ${duration} 秒"
}

# Handle script arguments
case "${1:-}" in
    ""|"restore")
        main "$2" "$3"
        ;;
    "list")
        list_backups
        ;;
    "verify")
        if [ -z "$2" ]; then
            error "请指定要验证的备份文件"
            exit 1
        fi
        backup_file=$(select_backup "$2")
        verify_backup "$backup_file"
        ;;
    "help"|"-h"|"--help")
        echo "Black Duck 恢复脚本"
        echo ""
        echo "用法: $0 [命令] [备份文件] [选项]"
        echo ""
        echo "命令:"
        echo "  (无), restore    执行数据恢复 (默认)"
        echo "  list             列出可用备份"
        echo "  verify [file]    验证备份文件"
        echo "  help             显示此帮助信息"
        echo ""
        echo "选项:"
        echo "  --force          强制恢复，不询问确认"
        echo ""
        echo "示例:"
        echo "  $0                                    # 交互式选择备份文件"
        echo "  $0 blackduck_backup_20250130_120000  # 恢复指定备份"
        echo "  $0 restore backup.tar.gz --force     # 强制恢复"
        echo "  $0 list                               # 列出备份文件"
        echo "  $0 verify backup.tar.gz               # 验证备份文件"
        echo ""
        ;;
    *)
        error "未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac
