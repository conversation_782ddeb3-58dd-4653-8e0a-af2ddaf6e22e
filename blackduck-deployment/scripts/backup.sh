#!/bin/bash

# Black Duck Backup Script
# Author: Deployment Automation
# Version: 1.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${PROJECT_DIR}/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="blackduck_backup_${TIMESTAMP}"
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# Database configuration
DB_CONTAINER="blackduck-postgres"
DB_NAME="bds_hub"
DB_USER="blackduck_user"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create backup directory
create_backup_dir() {
    log "创建备份目录..."
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
    success "备份目录创建完成: $BACKUP_DIR/$BACKUP_NAME"
}

# Backup database
backup_database() {
    log "备份 PostgreSQL 数据库..."
    
    local db_backup_file="$BACKUP_DIR/$BACKUP_NAME/database.sql"
    
    if docker exec "$DB_CONTAINER" pg_dump -U "$DB_USER" -d "$DB_NAME" > "$db_backup_file"; then
        success "数据库备份完成"
        
        # Compress database backup
        gzip "$db_backup_file"
        success "数据库备份已压缩: ${db_backup_file}.gz"
    else
        error "数据库备份失败"
        return 1
    fi
}

# Backup Docker volumes
backup_volumes() {
    log "备份 Docker 数据卷..."
    
    local volumes_dir="$BACKUP_DIR/$BACKUP_NAME/volumes"
    mkdir -p "$volumes_dir"
    
    # Get list of Black Duck volumes
    local volumes=$(docker volume ls --filter name=blackduck --format "{{.Name}}")
    
    for volume in $volumes; do
        log "备份数据卷: $volume"
        
        # Create tar archive of volume
        docker run --rm \
            -v "$volume:/data:ro" \
            -v "$volumes_dir:/backup" \
            alpine:latest \
            tar czf "/backup/${volume}.tar.gz" -C /data .
        
        if [ $? -eq 0 ]; then
            success "数据卷 $volume 备份完成"
        else
            error "数据卷 $volume 备份失败"
        fi
    done
}

# Backup configuration files
backup_configs() {
    log "备份配置文件..."
    
    local config_dir="$BACKUP_DIR/$BACKUP_NAME/configs"
    mkdir -p "$config_dir"
    
    # Copy configuration files
    cp -r "$PROJECT_DIR/configs" "$config_dir/" 2>/dev/null || true
    cp "$PROJECT_DIR/.env" "$config_dir/" 2>/dev/null || true
    cp "$PROJECT_DIR/docker-compose.yml" "$config_dir/" 2>/dev/null || true
    
    success "配置文件备份完成"
}

# Backup logs (recent only)
backup_logs() {
    log "备份最近的日志文件..."
    
    local logs_dir="$BACKUP_DIR/$BACKUP_NAME/logs"
    mkdir -p "$logs_dir"
    
    # Copy recent logs (last 7 days)
    find "$PROJECT_DIR/volumes/logs" -name "*.log" -mtime -7 -exec cp {} "$logs_dir/" \; 2>/dev/null || true
    
    # Compress logs
    if [ "$(ls -A "$logs_dir")" ]; then
        tar czf "$logs_dir.tar.gz" -C "$logs_dir" .
        rm -rf "$logs_dir"
        success "日志文件备份并压缩完成"
    else
        warning "未找到最近的日志文件"
        rmdir "$logs_dir" 2>/dev/null || true
    fi
}

# Create backup metadata
create_metadata() {
    log "创建备份元数据..."
    
    local metadata_file="$BACKUP_DIR/$BACKUP_NAME/backup_info.txt"
    
    {
        echo "Black Duck 备份信息"
        echo "===================="
        echo "备份时间: $(date)"
        echo "备份名称: $BACKUP_NAME"
        echo "Black Duck 版本: $(grep BLACKDUCK_VERSION "$PROJECT_DIR/.env" | cut -d'=' -f2)"
        echo ""
        echo "备份内容:"
        echo "- PostgreSQL 数据库"
        echo "- Docker 数据卷"
        echo "- 配置文件"
        echo "- 最近日志文件"
        echo ""
        echo "系统信息:"
        echo "- 主机名: $(hostname)"
        echo "- 操作系统: $(uname -a)"
        echo "- Docker 版本: $(docker --version)"
        echo ""
        echo "备份大小:"
        du -sh "$BACKUP_DIR/$BACKUP_NAME" | cut -f1
    } > "$metadata_file"
    
    success "备份元数据创建完成"
}

# Create final archive
create_archive() {
    log "创建最终备份归档..."
    
    cd "$BACKUP_DIR"
    tar czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    
    if [ $? -eq 0 ]; then
        # Remove uncompressed backup directory
        rm -rf "$BACKUP_NAME"
        
        local archive_size=$(du -sh "${BACKUP_NAME}.tar.gz" | cut -f1)
        success "备份归档创建完成: ${BACKUP_NAME}.tar.gz (大小: $archive_size)"
    else
        error "备份归档创建失败"
        return 1
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "清理旧备份文件..."
    
    local deleted_count=0
    
    # Find and delete backups older than retention period
    find "$BACKUP_DIR" -name "blackduck_backup_*.tar.gz" -mtime +$RETENTION_DAYS -type f | while read -r old_backup; do
        log "删除旧备份: $(basename "$old_backup")"
        rm -f "$old_backup"
        ((deleted_count++))
    done
    
    if [ $deleted_count -gt 0 ]; then
        success "已删除 $deleted_count 个旧备份文件"
    else
        log "未找到需要清理的旧备份文件"
    fi
}

# Verify backup
verify_backup() {
    log "验证备份完整性..."
    
    local archive_path="$BACKUP_DIR/${BACKUP_NAME}.tar.gz"
    
    if [ -f "$archive_path" ]; then
        # Test archive integrity
        if tar tzf "$archive_path" > /dev/null 2>&1; then
            success "备份归档完整性验证通过"
        else
            error "备份归档完整性验证失败"
            return 1
        fi
        
        # Check if database backup exists in archive
        if tar tzf "$archive_path" | grep -q "database.sql.gz"; then
            success "数据库备份文件存在于归档中"
        else
            warning "数据库备份文件未找到于归档中"
        fi
    else
        error "备份归档文件不存在"
        return 1
    fi
}

# Send notification (optional)
send_notification() {
    local status=$1
    local message=$2
    
    # Add your notification logic here (email, Slack, etc.)
    # Example for email:
    # echo "$message" | mail -s "Black Duck Backup $status" <EMAIL>
    
    log "通知: $status - $message"
}

# Main backup function
main() {
    log "开始 Black Duck 备份..."
    
    # Check if Black Duck is running
    if ! docker ps | grep -q blackduck; then
        warning "Black Duck 服务未运行，继续备份..."
    fi
    
    local start_time=$(date +%s)
    
    # Perform backup steps
    create_backup_dir
    backup_database
    backup_volumes
    backup_configs
    backup_logs
    create_metadata
    create_archive
    verify_backup
    cleanup_old_backups
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    success "Black Duck 备份完成！"
    log "备份耗时: ${duration} 秒"
    log "备份文件: $BACKUP_DIR/${BACKUP_NAME}.tar.gz"
    
    send_notification "SUCCESS" "Black Duck 备份成功完成，耗时 ${duration} 秒"
}

# Handle script arguments
case "${1:-}" in
    ""|"full")
        main
        ;;
    "database")
        log "仅备份数据库..."
        create_backup_dir
        backup_database
        create_metadata
        success "数据库备份完成"
        ;;
    "volumes")
        log "仅备份数据卷..."
        create_backup_dir
        backup_volumes
        create_metadata
        success "数据卷备份完成"
        ;;
    "configs")
        log "仅备份配置文件..."
        create_backup_dir
        backup_configs
        create_metadata
        success "配置文件备份完成"
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    "list")
        log "列出现有备份:"
        ls -lah "$BACKUP_DIR"/blackduck_backup_*.tar.gz 2>/dev/null || log "未找到备份文件"
        ;;
    "help"|"-h"|"--help")
        echo "Black Duck 备份脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  (无), full       执行完整备份 (默认)"
        echo "  database         仅备份数据库"
        echo "  volumes          仅备份数据卷"
        echo "  configs          仅备份配置文件"
        echo "  cleanup          清理旧备份文件"
        echo "  list             列出现有备份"
        echo "  help             显示此帮助信息"
        echo ""
        echo "环境变量:"
        echo "  BACKUP_RETENTION_DAYS    备份保留天数 (默认: 30)"
        echo ""
        ;;
    *)
        error "未知选项: $1"
        echo "使用 '$0 help' 查看可用选项"
        exit 1
        ;;
esac
